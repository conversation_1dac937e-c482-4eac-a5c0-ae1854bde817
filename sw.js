/**
 * Service Worker - مو<PERSON>د المدونات الذكي
 * يوفر إمكانية العمل دون اتصال وتحسين الأداء
 */

const CACHE_NAME = 'blog-generator-v1.0.0';
const STATIC_CACHE = 'blog-generator-static-v1.0.0';
const DYNAMIC_CACHE = 'blog-generator-dynamic-v1.0.0';

// الملفات الأساسية للتخزين المؤقت
const STATIC_FILES = [
    '/',
    '/index.html',
    '/test-blog.html',
    '/examples/basic-usage.html',
    '/assets/css/main.css',
    '/assets/js/app.js',
    '/assets/js/gemini-api.js',
    '/assets/js/blog-generator.js',
    '/assets/js/export-manager.js',
    '/assets/js/search-engine.js',
    '/assets/js/icon-manager.js',
    '/assets/js/settings-manager.js',
    '/assets/js/notification-manager.js',
    '/assets/js/analytics-manager.js',
    '/assets/icons/favicon.svg',
    '/templates/blog-template.html',
    '/README.md',
    '/CHANGELOG.md'
];

// الموارد الخارجية للتخزين المؤقت
const EXTERNAL_RESOURCES = [
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        Promise.all([
            // تخزين الملفات الثابتة
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            }),
            
            // تخزين الموارد الخارجية
            caches.open(DYNAMIC_CACHE).then(cache => {
                console.log('Service Worker: Caching external resources');
                return cache.addAll(EXTERNAL_RESOURCES);
            })
        ]).then(() => {
            console.log('Service Worker: Installation complete');
            return self.skipWaiting();
        }).catch(error => {
            console.error('Service Worker: Installation failed', error);
        })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    // حذف الذاكرة المؤقتة القديمة
                    if (cacheName !== STATIC_CACHE && 
                        cacheName !== DYNAMIC_CACHE && 
                        cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activation complete');
            return self.clients.claim();
        })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // تجاهل طلبات غير HTTP
    if (!request.url.startsWith('http')) {
        return;
    }
    
    // تجاهل طلبات POST
    if (request.method !== 'GET') {
        return;
    }
    
    // استراتيجية مختلفة حسب نوع الطلب
    if (url.origin === location.origin) {
        // الملفات المحلية - Cache First
        event.respondWith(cacheFirst(request));
    } else if (isExternalResource(request.url)) {
        // الموارد الخارجية - Stale While Revalidate
        event.respondWith(staleWhileRevalidate(request));
    } else if (isAPIRequest(request.url)) {
        // طلبات API - Network First
        event.respondWith(networkFirst(request));
    } else {
        // باقي الطلبات - Network First
        event.respondWith(networkFirst(request));
    }
});

/**
 * استراتيجية Cache First
 * البحث في الذاكرة المؤقتة أولاً، ثم الشبكة
 */
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        // تخزين الاستجابة الجديدة
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('Cache First failed:', error);
        
        // إرجاع صفحة offline إذا كانت متاحة
        if (request.destination === 'document') {
            const offlinePage = await caches.match('/offline.html');
            if (offlinePage) {
                return offlinePage;
            }
        }
        
        throw error;
    }
}

/**
 * استراتيجية Network First
 * محاولة الشبكة أولاً، ثم الذاكرة المؤقتة
 */
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed, trying cache:', error);
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

/**
 * استراتيجية Stale While Revalidate
 * إرجاع النسخة المخزنة فوراً وتحديثها في الخلفية
 */
async function staleWhileRevalidate(request) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);
    
    // تحديث الذاكرة المؤقتة في الخلفية
    const fetchPromise = fetch(request).then(networkResponse => {
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    }).catch(error => {
        console.log('Background fetch failed:', error);
    });
    
    // إرجاع النسخة المخزنة فوراً أو انتظار الشبكة
    return cachedResponse || fetchPromise;
}

/**
 * فحص ما إذا كان الطلب لمورد خارجي
 */
function isExternalResource(url) {
    return EXTERNAL_RESOURCES.some(resource => url.includes(resource)) ||
           url.includes('fonts.googleapis.com') ||
           url.includes('fonts.gstatic.com') ||
           url.includes('cdn.jsdelivr.net') ||
           url.includes('cdnjs.cloudflare.com');
}

/**
 * فحص ما إذا كان الطلب لـ API
 */
function isAPIRequest(url) {
    return url.includes('generativelanguage.googleapis.com') ||
           url.includes('api.') ||
           url.includes('/api/');
}

// معالجة رسائل من الصفحة الرئيسية
self.addEventListener('message', event => {
    const { type, data } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize().then(size => {
                event.ports[0].postMessage({ type: 'CACHE_SIZE', size });
            });
            break;
            
        case 'CLEAR_CACHE':
            clearCache().then(success => {
                event.ports[0].postMessage({ type: 'CACHE_CLEARED', success });
            });
            break;
            
        case 'UPDATE_CACHE':
            updateCache().then(success => {
                event.ports[0].postMessage({ type: 'CACHE_UPDATED', success });
            });
            break;
    }
});

/**
 * حساب حجم الذاكرة المؤقتة
 */
async function getCacheSize() {
    try {
        const cacheNames = await caches.keys();
        let totalSize = 0;
        
        for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName);
            const requests = await cache.keys();
            
            for (const request of requests) {
                const response = await cache.match(request);
                if (response) {
                    const blob = await response.blob();
                    totalSize += blob.size;
                }
            }
        }
        
        return totalSize;
    } catch (error) {
        console.error('Error calculating cache size:', error);
        return 0;
    }
}

/**
 * مسح الذاكرة المؤقتة
 */
async function clearCache() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
        );
        return true;
    } catch (error) {
        console.error('Error clearing cache:', error);
        return false;
    }
}

/**
 * تحديث الذاكرة المؤقتة
 */
async function updateCache() {
    try {
        const cache = await caches.open(STATIC_CACHE);
        await cache.addAll(STATIC_FILES);
        
        const dynamicCache = await caches.open(DYNAMIC_CACHE);
        await dynamicCache.addAll(EXTERNAL_RESOURCES);
        
        return true;
    } catch (error) {
        console.error('Error updating cache:', error);
        return false;
    }
}

// معالجة أخطاء غير متوقعة
self.addEventListener('error', event => {
    console.error('Service Worker error:', event.error);
});

self.addEventListener('unhandledrejection', event => {
    console.error('Service Worker unhandled rejection:', event.reason);
});

console.log('Service Worker: Script loaded');
