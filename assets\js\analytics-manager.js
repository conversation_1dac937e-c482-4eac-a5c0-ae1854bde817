/**
 * Analytics Manager - مدير الإحصائيات والتحليلات
 * يجمع ويحلل بيانات الاستخدام (مجهولة الهوية)
 */

class AnalyticsManager {
    constructor() {
        this.isEnabled = false;
        this.sessionId = this.generateSessionId();
        this.startTime = Date.now();
        this.events = [];
        this.userAgent = navigator.userAgent;
        this.language = navigator.language;
        this.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        
        this.eventTypes = {
            PAGE_LOAD: 'page_load',
            BLOG_GENERATE_START: 'blog_generate_start',
            BLOG_GENERATE_SUCCESS: 'blog_generate_success',
            BLOG_GENERATE_ERROR: 'blog_generate_error',
            EXPORT_START: 'export_start',
            EXPORT_SUCCESS: 'export_success',
            EXPORT_ERROR: 'export_error',
            SEARCH_PERFORMED: 'search_performed',
            SETTINGS_CHANGED: 'settings_changed',
            FORM_VALIDATION_ERROR: 'form_validation_error',
            API_ERROR: 'api_error',
            USER_INTERACTION: 'user_interaction'
        };
        
        this.init();
    }
    
    /**
     * تهيئة مدير التحليلات
     */
    init() {
        // التحقق من إعدادات الخصوصية
        this.checkPrivacySettings();
        
        if (this.isEnabled) {
            this.trackPageLoad();
            this.setupEventListeners();
            this.startSessionTracking();
        }
    }
    
    /**
     * التحقق من إعدادات الخصوصية
     */
    checkPrivacySettings() {
        // التحقق من إعدادات المستخدم
        const settings = window.settingsManager?.getSetting('analytics');
        if (settings !== undefined) {
            this.isEnabled = settings;
            return;
        }
        
        // التحقق من Do Not Track
        if (navigator.doNotTrack === '1' || window.doNotTrack === '1') {
            this.isEnabled = false;
            return;
        }
        
        // افتراضياً معطل حتى يوافق المستخدم
        this.isEnabled = false;
    }
    
    /**
     * تفعيل التحليلات
     */
    enable() {
        this.isEnabled = true;
        this.trackEvent(this.eventTypes.USER_INTERACTION, {
            action: 'analytics_enabled'
        });
    }
    
    /**
     * تعطيل التحليلات
     */
    disable() {
        this.isEnabled = false;
        this.clearStoredData();
    }
    
    /**
     * تتبع حدث
     */
    trackEvent(eventType, data = {}) {
        if (!this.isEnabled) return;
        
        const event = {
            id: this.generateEventId(),
            sessionId: this.sessionId,
            type: eventType,
            timestamp: Date.now(),
            data: this.sanitizeData(data),
            url: window.location.pathname,
            referrer: document.referrer || null,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
        
        this.events.push(event);
        this.saveEventLocally(event);
        
        // إرسال الأحداث المتراكمة كل 10 أحداث أو كل 5 دقائق
        if (this.events.length >= 10 || this.shouldSendBatch()) {
            this.sendBatch();
        }
    }
    
    /**
     * تنظيف البيانات الحساسة
     */
    sanitizeData(data) {
        const sanitized = { ...data };
        
        // إزالة البيانات الحساسة
        const sensitiveKeys = ['password', 'email', 'phone', 'apiKey', 'token'];
        sensitiveKeys.forEach(key => {
            if (sanitized[key]) {
                delete sanitized[key];
            }
        });
        
        // تشفير أو إخفاء البيانات الشخصية
        if (sanitized.blogName) {
            sanitized.blogNameLength = sanitized.blogName.length;
            delete sanitized.blogName;
        }
        
        if (sanitized.keywords) {
            sanitized.keywordsCount = sanitized.keywords.split(',').length;
            delete sanitized.keywords;
        }
        
        return sanitized;
    }
    
    /**
     * تتبع تحميل الصفحة
     */
    trackPageLoad() {
        this.trackEvent(this.eventTypes.PAGE_LOAD, {
            loadTime: Date.now() - this.startTime,
            userAgent: this.userAgent,
            language: this.language,
            timezone: this.timezone,
            screenResolution: `${screen.width}x${screen.height}`,
            colorDepth: screen.colorDepth
        });
    }
    
    /**
     * تتبع بدء توليد المدونة
     */
    trackBlogGenerationStart(formData) {
        this.trackEvent(this.eventTypes.BLOG_GENERATE_START, {
            language: formData.language,
            complexity: formData.complexity,
            sectionsCount: formData.sections ? formData.sections.split(',').length : 0,
            articlesPerSection: formData.articlesPerSection,
            includeSearch: formData.includeSearch,
            seoOptimized: formData.seoOptimized
        });
    }
    
    /**
     * تتبع نجاح توليد المدونة
     */
    trackBlogGenerationSuccess(generationTime, blogData) {
        this.trackEvent(this.eventTypes.BLOG_GENERATE_SUCCESS, {
            generationTime,
            sectionsGenerated: blogData.sections?.length || 0,
            articlesGenerated: blogData.articles?.length || 0,
            pagesGenerated: blogData.additionalPages?.length || 0
        });
    }
    
    /**
     * تتبع خطأ في توليد المدونة
     */
    trackBlogGenerationError(error, formData) {
        this.trackEvent(this.eventTypes.BLOG_GENERATE_ERROR, {
            errorType: error.name || 'Unknown',
            errorMessage: error.message?.substring(0, 100) || 'Unknown error',
            language: formData.language,
            complexity: formData.complexity
        });
    }
    
    /**
     * تتبع بدء التصدير
     */
    trackExportStart(format) {
        this.trackEvent(this.eventTypes.EXPORT_START, {
            format
        });
    }
    
    /**
     * تتبع نجاح التصدير
     */
    trackExportSuccess(format, exportTime) {
        this.trackEvent(this.eventTypes.EXPORT_SUCCESS, {
            format,
            exportTime
        });
    }
    
    /**
     * تتبع خطأ في التصدير
     */
    trackExportError(format, error) {
        this.trackEvent(this.eventTypes.EXPORT_ERROR, {
            format,
            errorType: error.name || 'Unknown',
            errorMessage: error.message?.substring(0, 100) || 'Unknown error'
        });
    }
    
    /**
     * تتبع البحث
     */
    trackSearch(query, resultsCount) {
        this.trackEvent(this.eventTypes.SEARCH_PERFORMED, {
            queryLength: query.length,
            resultsCount,
            hasResults: resultsCount > 0
        });
    }
    
    /**
     * تتبع تغيير الإعدادات
     */
    trackSettingsChange(setting, oldValue, newValue) {
        this.trackEvent(this.eventTypes.SETTINGS_CHANGED, {
            setting,
            oldValueType: typeof oldValue,
            newValueType: typeof newValue,
            changed: oldValue !== newValue
        });
    }
    
    /**
     * تتبع خطأ في التحقق من النموذج
     */
    trackFormValidationError(field, errorType) {
        this.trackEvent(this.eventTypes.FORM_VALIDATION_ERROR, {
            field,
            errorType
        });
    }
    
    /**
     * تتبع خطأ في API
     */
    trackAPIError(apiName, errorCode, errorMessage) {
        this.trackEvent(this.eventTypes.API_ERROR, {
            apiName,
            errorCode,
            errorMessage: errorMessage?.substring(0, 100) || 'Unknown error'
        });
    }
    
    /**
     * تتبع تفاعل المستخدم
     */
    trackUserInteraction(action, element, data = {}) {
        this.trackEvent(this.eventTypes.USER_INTERACTION, {
            action,
            element,
            ...data
        });
    }
    
    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تتبع النقرات على الأزرار المهمة
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button');
            if (button) {
                const action = button.id || button.className || 'unknown_button';
                this.trackUserInteraction('button_click', action);
            }
        });
        
        // تتبع تغييرات النموذج
        document.addEventListener('change', (e) => {
            if (e.target.form && e.target.form.id === 'blogGeneratorForm') {
                this.trackUserInteraction('form_field_change', e.target.id || e.target.name);
            }
        });
        
        // تتبع الأخطاء في JavaScript
        window.addEventListener('error', (e) => {
            this.trackEvent(this.eventTypes.API_ERROR, {
                apiName: 'javascript',
                errorCode: 'runtime_error',
                errorMessage: e.message?.substring(0, 100) || 'Unknown error',
                filename: e.filename,
                lineno: e.lineno
            });
        });
        
        // تتبع مغادرة الصفحة
        window.addEventListener('beforeunload', () => {
            this.trackSessionEnd();
            this.sendBatch(true); // إرسال فوري
        });
    }
    
    /**
     * بدء تتبع الجلسة
     */
    startSessionTracking() {
        // تتبع مدة الجلسة كل دقيقة
        setInterval(() => {
            this.trackUserInteraction('session_heartbeat', 'timer', {
                sessionDuration: Date.now() - this.startTime
            });
        }, 60000);
    }
    
    /**
     * تتبع انتهاء الجلسة
     */
    trackSessionEnd() {
        this.trackUserInteraction('session_end', 'page_unload', {
            sessionDuration: Date.now() - this.startTime,
            eventsCount: this.events.length
        });
    }
    
    /**
     * فحص ما إذا كان يجب إرسال الدفعة
     */
    shouldSendBatch() {
        const lastSent = localStorage.getItem('analytics_last_sent');
        if (!lastSent) return true;
        
        const fiveMinutes = 5 * 60 * 1000;
        return Date.now() - parseInt(lastSent) > fiveMinutes;
    }
    
    /**
     * إرسال دفعة من الأحداث
     */
    async sendBatch(immediate = false) {
        if (!this.isEnabled || this.events.length === 0) return;
        
        const eventsToSend = [...this.events];
        this.events = [];
        
        try {
            // في بيئة الإنتاج، يمكن إرسال البيانات إلى خدمة التحليلات
            // هنا نحفظها محلياً فقط للعرض التوضيحي
            this.saveAnalyticsData(eventsToSend);
            
            localStorage.setItem('analytics_last_sent', Date.now().toString());
            
        } catch (error) {
            console.warn('فشل في إرسال بيانات التحليلات:', error);
            // إعادة الأحداث إلى القائمة في حالة الفشل
            this.events.unshift(...eventsToSend);
        }
    }
    
    /**
     * حفظ حدث محلياً
     */
    saveEventLocally(event) {
        try {
            const stored = JSON.parse(localStorage.getItem('analytics_events') || '[]');
            stored.push(event);
            
            // الاحتفاظ بآخر 1000 حدث فقط
            if (stored.length > 1000) {
                stored.splice(0, stored.length - 1000);
            }
            
            localStorage.setItem('analytics_events', JSON.stringify(stored));
        } catch (error) {
            console.warn('فشل في حفظ حدث التحليلات محلياً:', error);
        }
    }
    
    /**
     * حفظ بيانات التحليلات
     */
    saveAnalyticsData(events) {
        try {
            const analytics = JSON.parse(localStorage.getItem('analytics_data') || '{}');
            
            if (!analytics.sessions) analytics.sessions = [];
            if (!analytics.events) analytics.events = [];
            if (!analytics.summary) analytics.summary = {};
            
            // إضافة الأحداث
            analytics.events.push(...events);
            
            // تحديث الملخص
            this.updateSummary(analytics.summary, events);
            
            localStorage.setItem('analytics_data', JSON.stringify(analytics));
        } catch (error) {
            console.warn('فشل في حفظ بيانات التحليلات:', error);
        }
    }
    
    /**
     * تحديث ملخص التحليلات
     */
    updateSummary(summary, events) {
        events.forEach(event => {
            if (!summary[event.type]) {
                summary[event.type] = 0;
            }
            summary[event.type]++;
        });
        
        summary.lastUpdated = Date.now();
        summary.totalEvents = (summary.totalEvents || 0) + events.length;
    }
    
    /**
     * الحصول على إحصائيات الاستخدام
     */
    getUsageStats() {
        try {
            const analytics = JSON.parse(localStorage.getItem('analytics_data') || '{}');
            return analytics.summary || {};
        } catch (error) {
            console.warn('فشل في قراءة إحصائيات الاستخدام:', error);
            return {};
        }
    }
    
    /**
     * مسح البيانات المحفوظة
     */
    clearStoredData() {
        localStorage.removeItem('analytics_events');
        localStorage.removeItem('analytics_data');
        localStorage.removeItem('analytics_last_sent');
    }
    
    /**
     * توليد معرف الجلسة
     */
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * توليد معرف الحدث
     */
    generateEventId() {
        return 'event_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * تصدير بيانات التحليلات
     */
    exportAnalytics() {
        const analytics = JSON.parse(localStorage.getItem('analytics_data') || '{}');
        const blob = new Blob([JSON.stringify(analytics, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
}

// إنشاء مثيل عام من مدير التحليلات
window.analyticsManager = new AnalyticsManager();
