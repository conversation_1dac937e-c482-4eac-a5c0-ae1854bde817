const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const WorkboxPlugin = require('workbox-webpack-plugin');

const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
    mode: isProduction ? 'production' : 'development',
    
    entry: {
        main: './assets/js/app.js',
        'gemini-api': './assets/js/gemini-api.js',
        'blog-generator': './assets/js/blog-generator.js',
        'export-manager': './assets/js/export-manager.js',
        'search-engine': './assets/js/search-engine.js',
        'icon-manager': './assets/js/icon-manager.js',
        'settings-manager': './assets/js/settings-manager.js',
        'notification-manager': './assets/js/notification-manager.js',
        'analytics-manager': './assets/js/analytics-manager.js'
    },
    
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: isProduction ? 'js/[name].[contenthash].js' : 'js/[name].js',
        chunkFilename: isProduction ? 'js/[name].[contenthash].chunk.js' : 'js/[name].chunk.js',
        assetModuleFilename: 'assets/[name].[contenthash][ext]',
        clean: true
    },
    
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    
    devServer: {
        static: {
            directory: path.join(__dirname, 'dist'),
        },
        port: 8080,
        open: true,
        hot: true,
        compress: true,
        historyApiFallback: true,
        client: {
            overlay: {
                errors: true,
                warnings: false,
            },
        },
    },
    
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
                    options: {
                        presets: [
                            ['@babel/preset-env', {
                                targets: {
                                    browsers: ['> 1%', 'last 2 versions', 'not ie 11']
                                },
                                useBuiltIns: 'usage',
                                corejs: 3
                            }]
                        ],
                        plugins: [
                            '@babel/plugin-proposal-class-properties',
                            '@babel/plugin-proposal-optional-chaining',
                            '@babel/plugin-proposal-nullish-coalescing-operator'
                        ]
                    }
                }
            },
            {
                test: /\.css$/,
                use: [
                    isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
                    'css-loader',
                    {
                        loader: 'postcss-loader',
                        options: {
                            postcssOptions: {
                                plugins: [
                                    ['autoprefixer'],
                                    ['cssnano', { preset: 'default' }]
                                ]
                            }
                        }
                    }
                ]
            },
            {
                test: /\.(png|jpe?g|gif|svg|ico)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'images/[name].[contenthash][ext]'
                }
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/i,
                type: 'asset/resource',
                generator: {
                    filename: 'fonts/[name].[contenthash][ext]'
                }
            },
            {
                test: /\.html$/,
                use: [
                    {
                        loader: 'html-loader',
                        options: {
                            sources: {
                                list: [
                                    {
                                        tag: 'img',
                                        attribute: 'src',
                                        type: 'src'
                                    },
                                    {
                                        tag: 'link',
                                        attribute: 'href',
                                        type: 'src',
                                        filter: (tag, attribute, attributes) => {
                                            return attributes.rel && attributes.rel.includes('icon');
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        ]
    },
    
    plugins: [
        new CleanWebpackPlugin(),
        
        new HtmlWebpackPlugin({
            template: './index.html',
            filename: 'index.html',
            chunks: ['main'],
            minify: isProduction ? {
                removeComments: true,
                collapseWhitespace: true,
                removeRedundantAttributes: true,
                useShortDoctype: true,
                removeEmptyAttributes: true,
                removeStyleLinkTypeAttributes: true,
                keepClosingSlash: true,
                minifyJS: true,
                minifyCSS: true,
                minifyURLs: true
            } : false
        }),
        
        new HtmlWebpackPlugin({
            template: './test-blog.html',
            filename: 'test-blog.html',
            chunks: ['main'],
            minify: isProduction
        }),
        
        new HtmlWebpackPlugin({
            template: './examples/basic-usage.html',
            filename: 'examples/basic-usage.html',
            chunks: ['main'],
            minify: isProduction
        }),
        
        ...(isProduction ? [
            new MiniCssExtractPlugin({
                filename: 'css/[name].[contenthash].css',
                chunkFilename: 'css/[name].[contenthash].chunk.css'
            })
        ] : []),
        
        new CopyWebpackPlugin({
            patterns: [
                {
                    from: 'assets/icons',
                    to: 'assets/icons'
                },
                {
                    from: 'templates',
                    to: 'templates'
                },
                {
                    from: 'docs',
                    to: 'docs'
                },
                {
                    from: 'README.md',
                    to: 'README.md'
                },
                {
                    from: 'CHANGELOG.md',
                    to: 'CHANGELOG.md'
                },
                {
                    from: 'LICENSE',
                    to: 'LICENSE'
                }
            ]
        }),
        
        ...(isProduction ? [
            new WorkboxPlugin.GenerateSW({
                clientsClaim: true,
                skipWaiting: true,
                runtimeCaching: [
                    {
                        urlPattern: /^https:\/\/fonts\.googleapis\.com/,
                        handler: 'StaleWhileRevalidate',
                        options: {
                            cacheName: 'google-fonts-stylesheets',
                        },
                    },
                    {
                        urlPattern: /^https:\/\/fonts\.gstatic\.com/,
                        handler: 'CacheFirst',
                        options: {
                            cacheName: 'google-fonts-webfonts',
                            expiration: {
                                maxEntries: 30,
                                maxAgeSeconds: 60 * 60 * 24 * 365, // 1 year
                            },
                        },
                    },
                    {
                        urlPattern: /^https:\/\/cdn\.jsdelivr\.net/,
                        handler: 'StaleWhileRevalidate',
                        options: {
                            cacheName: 'jsdelivr-cdn',
                        },
                    },
                    {
                        urlPattern: /^https:\/\/cdnjs\.cloudflare\.com/,
                        handler: 'StaleWhileRevalidate',
                        options: {
                            cacheName: 'cloudflare-cdn',
                        },
                    }
                ]
            })
        ] : [])
    ],
    
    optimization: {
        minimize: isProduction,
        minimizer: [
            new TerserPlugin({
                terserOptions: {
                    compress: {
                        drop_console: isProduction,
                        drop_debugger: isProduction
                    },
                    format: {
                        comments: false
                    }
                },
                extractComments: false
            }),
            new CssMinimizerPlugin()
        ],
        
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: 'vendors',
                    chunks: 'all',
                    priority: 10
                },
                common: {
                    name: 'common',
                    minChunks: 2,
                    chunks: 'all',
                    priority: 5,
                    reuseExistingChunk: true
                }
            }
        },
        
        runtimeChunk: {
            name: 'runtime'
        }
    },
    
    resolve: {
        extensions: ['.js', '.json'],
        alias: {
            '@': path.resolve(__dirname, 'assets'),
            '@js': path.resolve(__dirname, 'assets/js'),
            '@css': path.resolve(__dirname, 'assets/css'),
            '@icons': path.resolve(__dirname, 'assets/icons'),
            '@templates': path.resolve(__dirname, 'templates'),
            '@docs': path.resolve(__dirname, 'docs'),
            '@examples': path.resolve(__dirname, 'examples')
        }
    },
    
    externals: {
        // استبعاد المكتبات الخارجية من البناء
        'bootstrap': 'bootstrap',
        'jspdf': 'jspdf',
        'xlsx': 'XLSX',
        'jszip': 'JSZip'
    },
    
    performance: {
        hints: isProduction ? 'warning' : false,
        maxEntrypointSize: 512000,
        maxAssetSize: 512000
    },
    
    stats: {
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false
    }
};
