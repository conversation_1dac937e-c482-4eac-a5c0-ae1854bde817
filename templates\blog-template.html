<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{BLOG_TITLE}} - {{BLOG_NAME}}</title>
    <meta name="description" content="{{META_DESCRIPTION}}">
    <meta name="keywords" content="{{KEYWORDS}}">
    <meta name="author" content="{{AUTHOR}}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{SITE_URL}}">
    <meta property="og:title" content="{{BLOG_TITLE}}">
    <meta property="og:description" content="{{META_DESCRIPTION}}">
    <meta property="og:image" content="{{OG_IMAGE}}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{SITE_URL}}">
    <meta property="twitter:title" content="{{BLOG_TITLE}}">
    <meta property="twitter:description" content="{{META_DESCRIPTION}}">
    <meta property="twitter:image" content="{{TWITTER_IMAGE}}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{FAVICON_URL}}">
    <link rel="apple-touch-icon" href="{{APPLE_TOUCH_ICON}}">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        {{CUSTOM_CSS}}
    </style>
    
    <!-- Schema.org markup -->
    <script type="application/ld+json">
    {{SCHEMA_MARKUP}}
    </script>
</head>
<body>
    <!-- Header -->
    <header class="main-header sticky-top">
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="index.html">
                    {{#if LOGO_URL}}
                    <img src="{{LOGO_URL}}" alt="{{BLOG_NAME}}" height="40" class="me-2">
                    {{else}}
                    <i class="fas fa-blog text-primary fs-2 me-2"></i>
                    {{/if}}
                    <span class="fw-bold text-primary">{{BLOG_NAME}}</span>
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="index.html">الرئيسية</a>
                        </li>
                        {{#each SECTIONS}}
                        <li class="nav-item">
                            <a class="nav-link" href="sections/{{this.id}}.html">{{this.name}}</a>
                        </li>
                        {{/each}}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                المزيد
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="pages/about.html">من نحن</a></li>
                                <li><a class="dropdown-item" href="pages/contact.html">اتصل بنا</a></li>
                                <li><a class="dropdown-item" href="pages/privacy.html">سياسة الخصوصية</a></li>
                            </ul>
                        </li>
                    </ul>
                    
                    <!-- Search Box -->
                    {{#if INCLUDE_SEARCH}}
                    <form class="d-flex" role="search">
                        <div class="input-group">
                            <input class="form-control" type="search" id="searchInput" placeholder="ابحث في المدونة..." aria-label="Search">
                            <button class="btn btn-outline-primary" type="button" onclick="performSearch()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                    {{/if}}
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    {{#if HERO_ENABLED}}
    <section class="hero-section {{HERO_STYLE}}" style="{{HERO_BACKGROUND}}">
        <div class="container">
            <div class="row align-items-center min-vh-50">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="hero-title display-4 fw-bold mb-4">{{HERO_TITLE}}</h1>
                    <p class="hero-subtitle lead mb-4">{{HERO_SUBTITLE}}</p>
                    <div class="hero-actions">
                        <a href="#latest-articles" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-arrow-down"></i>
                            استكشف المقالات
                        </a>
                        <a href="pages/about.html" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-info-circle"></i>
                            تعرف علينا
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {{#if HERO_PARTICLES}}
        <div class="hero-particles"></div>
        {{/if}}
    </section>
    {{/if}}

    <!-- Main Content -->
    <main class="main-content">
        {{MAIN_CONTENT}}
    </main>

    <!-- Latest Articles Section -->
    <section id="latest-articles" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">أحدث المقالات</h2>
                    <p class="section-subtitle text-muted">اكتشف أحدث المحتوى في مدونتنا</p>
                </div>
            </div>
            <div class="row g-4">
                {{#each LATEST_ARTICLES}}
                <div class="col-lg-4 col-md-6">
                    <article class="card article-card h-100 shadow-sm">
                        {{#if this.image}}
                        <img src="{{this.image}}" class="card-img-top" alt="{{this.title}}" style="height: 200px; object-fit: cover;">
                        {{else}}
                        <div class="card-img-top bg-primary d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-{{this.icon}} text-white fa-3x"></i>
                        </div>
                        {{/if}}
                        <div class="card-body d-flex flex-column">
                            <div class="mb-2">
                                <span class="badge bg-primary">{{this.category}}</span>
                                <small class="text-muted ms-2">
                                    <i class="fas fa-clock"></i> {{this.readingTime}}
                                </small>
                            </div>
                            <h5 class="card-title">
                                <a href="articles/{{this.id}}.html" class="text-decoration-none">{{this.title}}</a>
                            </h5>
                            <p class="card-text flex-grow-1">{{this.excerpt}}</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-user"></i> {{this.author}}
                                    </small>
                                    <a href="articles/{{this.id}}.html" class="btn btn-outline-primary btn-sm">
                                        اقرأ المزيد
                                    </a>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
                {{/each}}
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">تصفح حسب الفئة</h2>
                    <p class="section-subtitle text-muted">اختر الموضوع الذي يهمك</p>
                </div>
            </div>
            <div class="row g-4">
                {{#each SECTIONS}}
                <div class="col-lg-4 col-md-6">
                    <div class="category-card card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="category-icon mb-3">
                                <i class="fas fa-{{this.icon}} fa-3x text-primary"></i>
                            </div>
                            <h4 class="card-title">{{this.name}}</h4>
                            <p class="card-text">{{this.description}}</p>
                            <div class="mb-3">
                                <span class="badge bg-light text-dark">{{this.articleCount}} مقال</span>
                            </div>
                            <a href="sections/{{this.id}}.html" class="btn btn-primary">
                                استكشف القسم
                            </a>
                        </div>
                    </div>
                </div>
                {{/each}}
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    {{#if NEWSLETTER_ENABLED}}
    <section class="py-5 bg-primary text-white">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-6 text-center">
                    <h3 class="mb-3">اشترك في النشرة الإخبارية</h3>
                    <p class="mb-4">احصل على أحدث المقالات والأخبار مباشرة في بريدك الإلكتروني</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="أدخل بريدك الإلكتروني" required>
                            <button class="btn btn-light" type="submit">
                                <i class="fas fa-paper-plane"></i>
                                اشتراك
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
    {{/if}}

    <!-- Footer -->
    <footer class="main-footer bg-dark text-light py-5">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="footer-brand">
                        <h4 class="mb-3">{{BLOG_NAME}}</h4>
                        <p class="mb-3">{{BLOG_DESCRIPTION}}</p>
                        <div class="social-links">
                            {{#each SOCIAL_LINKS}}
                            <a href="{{this.url}}" class="social-link me-2" target="_blank">
                                <i class="fab fa-{{this.platform}}"></i>
                            </a>
                            {{/each}}
                        </div>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h5 class="mb-3">روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.html" class="text-light-50">الرئيسية</a></li>
                        {{#each SECTIONS}}
                        <li><a href="sections/{{this.id}}.html" class="text-light-50">{{this.name}}</a></li>
                        {{/each}}
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6">
                    <h5 class="mb-3">صفحات مهمة</h5>
                    <ul class="list-unstyled">
                        <li><a href="pages/about.html" class="text-light-50">من نحن</a></li>
                        <li><a href="pages/contact.html" class="text-light-50">اتصل بنا</a></li>
                        <li><a href="pages/privacy.html" class="text-light-50">سياسة الخصوصية</a></li>
                        <li><a href="pages/terms.html" class="text-light-50">شروط الاستخدام</a></li>
                    </ul>
                </div>
                <div class="col-lg-4">
                    <h5 class="mb-3">معلومات الاتصال</h5>
                    <div class="contact-info">
                        {{#if CONTACT_EMAIL}}
                        <p><i class="fas fa-envelope me-2"></i> {{CONTACT_EMAIL}}</p>
                        {{/if}}
                        {{#if CONTACT_PHONE}}
                        <p><i class="fas fa-phone me-2"></i> {{CONTACT_PHONE}}</p>
                        {{/if}}
                        {{#if CONTACT_ADDRESS}}
                        <p><i class="fas fa-map-marker-alt me-2"></i> {{CONTACT_ADDRESS}}</p>
                        {{/if}}
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">
                        &copy; {{CURRENT_YEAR}} {{BLOG_NAME}}. جميع الحقوق محفوظة.
                        <br>
                        <small class="text-muted">تم إنشاؤها بواسطة مولد المدونات الذكي</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-3" style="display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom Scripts -->
    <script>
        {{CUSTOM_JS}}
    </script>
    
    <!-- Analytics -->
    {{#if ANALYTICS_CODE}}
    <script>
        {{ANALYTICS_CODE}}
    </script>
    {{/if}}
</body>
</html>
