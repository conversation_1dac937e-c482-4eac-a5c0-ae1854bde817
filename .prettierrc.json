{"printWidth": 120, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": true, "quoteProps": "as-needed", "jsxSingleQuote": true, "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "rangeStart": 0, "rangeEnd": null, "requirePragma": false, "insertPragma": false, "proseWrap": "preserve", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "singleAttributePerLine": false, "overrides": [{"files": "*.html", "options": {"tabWidth": 2, "printWidth": 100}}, {"files": "*.css", "options": {"tabWidth": 2, "singleQuote": false}}, {"files": "*.md", "options": {"tabWidth": 2, "proseWrap": "always", "printWidth": 80}}, {"files": "*.json", "options": {"tabWidth": 2, "trailingComma": "none"}}]}