# مرجع API - مولد المدونات الذكي

## نظرة عامة

يوفر مولد المدونات الذكي مجموعة شاملة من APIs لإدارة جميع جوانب توليد المدونات.

## GeminiAPIManager

### الخصائص

```javascript
const geminiAPI = new GeminiAPIManager();
```

#### المفاتيح والإعدادات

- `apiKeys`: مصفوفة مفاتيح Gemini API
- `currentKeyIndex`: فهرس المفتاح الحالي
- `baseURL`: رابط API الأساسي
- `maxRetries`: عدد المحاولات القصوى
- `retryDelay`: تأخير إعادة المحاولة

### الطرق

#### `makeRequest(prompt, retryCount = 0)`

إرسال طلب إلى Gemini API مع إعادة المحاولة التلقائية.

**المعاملات:**
- `prompt` (string): النص المراد إرساله
- `retryCount` (number): عدد المحاولات الحالي

**العائد:** Promise<string>

**مثال:**
```javascript
const result = await geminiAPI.makeRequest('اكتب مقال عن الذكاء الاصطناعي');
console.log(result);
```

#### `rotateKey()`

التبديل إلى المفتاح التالي في القائمة.

**مثال:**
```javascript
geminiAPI.rotateKey();
```

#### `testAllKeys()`

اختبار جميع مفاتيح API والتحقق من صحتها.

**العائد:** Promise<Array>

**مثال:**
```javascript
const results = await geminiAPI.testAllKeys();
results.forEach(result => {
    console.log(`المفتاح ${result.keyIndex}: ${result.status}`);
});
```

#### `getStats()`

الحصول على إحصائيات الاستخدام.

**العائد:** Object

**مثال:**
```javascript
const stats = geminiAPI.getStats();
console.log(`معدل النجاح: ${stats.successRate}`);
```

## BlogGenerator

### الخصائص

```javascript
const blogGenerator = new BlogGenerator();
```

### الطرق

#### `generateBlog(blogData, progressCallback)`

توليد مدونة كاملة بناءً على البيانات المدخلة.

**المعاملات:**
- `blogData` (Object): بيانات المدونة
- `progressCallback` (Function): دالة تتبع التقدم

**العائد:** Promise<Object>

**مثال:**
```javascript
const blogData = {
    blogName: 'مدونة التقنية',
    language: 'ar',
    sections: 'تقنية، صحة، تعليم',
    keywords: 'الذكاء الاصطناعي، البرمجة'
};

const blog = await blogGenerator.generateBlog(blogData, (progress, message) => {
    console.log(`${progress}%: ${message}`);
});
```

#### `generateBlogStructure(blogData)`

إنشاء هيكل المدونة الأساسي.

**المعاملات:**
- `blogData` (Object): بيانات المدونة

**العائد:** Promise<Object>

#### `generateArticles(blogData, sections)`

توليد المقالات للأقسام المحددة.

**المعاملات:**
- `blogData` (Object): بيانات المدونة
- `sections` (Array): قائمة الأقسام

**العائد:** Promise<Array>

## ExportManager

### الطرق

#### `setBlog(blog)`

تعيين المدونة للتصدير.

**المعاملات:**
- `blog` (Object): بيانات المدونة

#### `exportAsHTML()`

تصدير المدونة بصيغة HTML.

**العائد:** Promise<string>

**مثال:**
```javascript
await exportManager.setBlog(generatedBlog);
const htmlContent = await exportManager.exportAsHTML();
```

#### `exportAsPDF()`

تصدير المدونة بصيغة PDF.

**العائد:** Promise<Blob>

#### `exportAsExcel()`

تصدير المدونة بصيغة Excel.

**العائد:** Promise<Workbook>

#### `exportAsZip()`

تصدير المدونة كملف مضغوط شامل.

**العائد:** Promise<Blob>

## SearchEngine

### الطرق

#### `indexContent(blogData)`

فهرسة محتوى المدونة للبحث.

**المعاملات:**
- `blogData` (Object): بيانات المدونة

**مثال:**
```javascript
searchEngine.indexContent({
    articles: [...],
    sections: [...],
    pages: [...]
});
```

#### `search(query, options = {})`

البحث في المحتوى المفهرس.

**المعاملات:**
- `query` (string): نص البحث
- `options` (Object): خيارات البحث

**العائد:** Array

**مثال:**
```javascript
const results = searchEngine.search('الذكاء الاصطناعي', {
    fuzzySearch: true,
    maxResults: 10
});
```

#### `getSuggestions(query, limit = 5)`

الحصول على اقتراحات البحث.

**المعاملات:**
- `query` (string): نص البحث الجزئي
- `limit` (number): عدد الاقتراحات

**العائد:** Array

## NotificationManager

### الطرق

#### `show(options)`

عرض إشعار مخصص.

**المعاملات:**
- `options` (Object): خيارات الإشعار

**العائد:** string (notification ID)

**مثال:**
```javascript
const id = notificationManager.show({
    type: 'success',
    title: 'نجح!',
    message: 'تم حفظ البيانات',
    duration: 5000,
    actions: [
        { text: 'تراجع', onClick: () => undo() }
    ]
});
```

#### `success(message, title, options)`

عرض إشعار نجاح.

**مثال:**
```javascript
notificationManager.success('تم إنشاء المدونة بنجاح!');
```

#### `error(message, title, options)`

عرض إشعار خطأ.

#### `warning(message, title, options)`

عرض إشعار تحذير.

#### `info(message, title, options)`

عرض إشعار معلومات.

#### `loading(message, title, options)`

عرض إشعار تحميل.

#### `hide(notificationId)`

إخفاء إشعار محدد.

#### `clear()`

مسح جميع الإشعارات.

## SettingsManager

### الطرق

#### `updateSetting(key, value)`

تحديث إعداد محدد.

**المعاملات:**
- `key` (string): مفتاح الإعداد
- `value` (any): القيمة الجديدة

**مثال:**
```javascript
settingsManager.updateSetting('theme', 'dark');
settingsManager.updateSetting('language', 'en');
```

#### `getSetting(key)`

الحصول على قيمة إعداد.

**المعاملات:**
- `key` (string): مفتاح الإعداد

**العائد:** any

#### `getAllSettings()`

الحصول على جميع الإعدادات.

**العائد:** Object

#### `resetSettings()`

إعادة تعيين الإعدادات للقيم الافتراضية.

#### `exportSettings()`

تصدير الإعدادات كملف JSON.

#### `importSettings(file)`

استيراد الإعدادات من ملف.

**المعاملات:**
- `file` (File): ملف الإعدادات

**العائد:** Promise<Object>

## IconManager

### الطرق

#### `getIconForTopic(topic, category = null)`

الحصول على أيقونة مناسبة للموضوع.

**المعاملات:**
- `topic` (string): الموضوع
- `category` (string): الفئة (اختياري)

**العائد:** string

**مثال:**
```javascript
const icon = iconManager.getIconForTopic('الذكاء الاصطناعي', 'technology');
// العائد: 'robot'
```

#### `getColorForCategory(category)`

الحصول على لون مناسب للفئة.

**المعاملات:**
- `category` (string): الفئة

**العائد:** string

#### `createCustomIcon(type, color, size)`

إنشاء أيقونة SVG مخصصة.

**المعاملات:**
- `type` (string): نوع الأيقونة
- `color` (string): اللون
- `size` (number): الحجم

**العائد:** string (SVG)

## AnalyticsManager

### الطرق

#### `enable()`

تفعيل التحليلات.

#### `disable()`

تعطيل التحليلات.

#### `trackEvent(eventType, data)`

تتبع حدث مخصص.

**المعاملات:**
- `eventType` (string): نوع الحدث
- `data` (Object): بيانات الحدث

**مثال:**
```javascript
analyticsManager.trackEvent('BLOG_GENERATE_START', {
    language: 'ar',
    sectionsCount: 5
});
```

#### `getUsageStats()`

الحصول على إحصائيات الاستخدام.

**العائد:** Object

## الأحداث المخصصة

### settingsUpdated

يتم إطلاقه عند تحديث الإعدادات.

```javascript
window.addEventListener('settingsUpdated', (e) => {
    console.log('تم تحديث:', e.detail.key, e.detail.value);
});
```

### blogGenerated

يتم إطلاقه عند إكمال توليد المدونة.

```javascript
window.addEventListener('blogGenerated', (e) => {
    console.log('تم إنشاء المدونة:', e.detail.blog);
});
```

### exportCompleted

يتم إطلاقه عند إكمال التصدير.

```javascript
window.addEventListener('exportCompleted', (e) => {
    console.log('تم التصدير بصيغة:', e.detail.format);
});
```

## معالجة الأخطاء

جميع الطرق التي تعيد Promise تستخدم معالجة أخطاء موحدة:

```javascript
try {
    const result = await blogGenerator.generateBlog(data);
    // معالجة النجاح
} catch (error) {
    console.error('خطأ:', error.message);
    notificationManager.error(error.message);
}
```

## أمثلة شاملة

### إنشاء مدونة كاملة

```javascript
async function createCompleteBlog() {
    try {
        // إعداد البيانات
        const blogData = {
            blogName: 'مدونة التقنية العربية',
            language: 'ar',
            sections: 'تقنية، صحة، تعليم، أعمال',
            keywords: 'الذكاء الاصطناعي، البرمجة، التطوير',
            articlesPerSection: 3,
            complexity: 'متوسط',
            seoOptimized: true
        };

        // توليد المدونة
        const blog = await blogGenerator.generateBlog(blogData, (progress, message) => {
            console.log(`${progress}%: ${message}`);
        });

        // فهرسة للبحث
        searchEngine.indexContent(blog);

        // إعداد التصدير
        exportManager.setBlog(blog);

        // إشعار النجاح
        notificationManager.success('تم إنشاء المدونة بنجاح!');

        return blog;
    } catch (error) {
        notificationManager.error(`فشل في إنشاء المدونة: ${error.message}`);
        throw error;
    }
}
```

### البحث والتصدير

```javascript
async function searchAndExport() {
    // البحث
    const results = searchEngine.search('الذكاء الاصطناعي');
    console.log(`تم العثور على ${results.length} نتيجة`);

    // التصدير
    await exportManager.exportAsHTML();
    notificationManager.success('تم تصدير المدونة بصيغة HTML');
}
```
