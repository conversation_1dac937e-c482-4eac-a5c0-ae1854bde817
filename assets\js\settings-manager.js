/**
 * Settings Manager - مدير الإعدادات والتخصيص
 * يدير إعدادات التطبيق والتخصيصات المختلفة
 */

class SettingsManager {
    constructor() {
        this.settings = {
            theme: 'light',
            language: 'ar',
            autoSave: true,
            notifications: true,
            animations: true,
            fontSize: 'medium',
            colorScheme: 'blue',
            layout: 'default',
            searchHistory: true,
            analytics: false
        };
        
        this.themes = {
            light: {
                name: 'فاتح',
                colors: {
                    primary: '#2563eb',
                    secondary: '#1e40af',
                    background: '#ffffff',
                    surface: '#f8fafc',
                    text: '#1f2937'
                }
            },
            dark: {
                name: 'داكن',
                colors: {
                    primary: '#3b82f6',
                    secondary: '#2563eb',
                    background: '#1f2937',
                    surface: '#374151',
                    text: '#f9fafb'
                }
            },
            auto: {
                name: 'تلقائي',
                colors: null // يتم تحديدها حسب نظام التشغيل
            }
        };
        
        this.colorSchemes = {
            blue: { primary: '#2563eb', secondary: '#1e40af', accent: '#3b82f6' },
            green: { primary: '#10b981', secondary: '#059669', accent: '#34d399' },
            purple: { primary: '#8b5cf6', secondary: '#7c3aed', accent: '#a78bfa' },
            red: { primary: '#ef4444', secondary: '#dc2626', accent: '#f87171' },
            orange: { primary: '#f97316', secondary: '#ea580c', accent: '#fb923c' },
            pink: { primary: '#ec4899', secondary: '#db2777', accent: '#f472b6' }
        };
        
        this.fontSizes = {
            small: { base: '14px', scale: 0.9 },
            medium: { base: '16px', scale: 1.0 },
            large: { base: '18px', scale: 1.1 },
            xlarge: { base: '20px', scale: 1.2 }
        };
        
        this.loadSettings();
        this.applySettings();
    }
    
    /**
     * تحميل الإعدادات من التخزين المحلي
     */
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem('blogGeneratorSettings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            }
        } catch (error) {
            console.warn('فشل في تحميل الإعدادات:', error);
        }
    }
    
    /**
     * حفظ الإعدادات في التخزين المحلي
     */
    saveSettings() {
        try {
            localStorage.setItem('blogGeneratorSettings', JSON.stringify(this.settings));
        } catch (error) {
            console.error('فشل في حفظ الإعدادات:', error);
        }
    }
    
    /**
     * تطبيق الإعدادات على التطبيق
     */
    applySettings() {
        this.applyTheme();
        this.applyColorScheme();
        this.applyFontSize();
        this.applyAnimations();
        this.applyLanguage();
    }
    
    /**
     * تطبيق السمة
     */
    applyTheme() {
        const theme = this.settings.theme;
        document.documentElement.setAttribute('data-theme', theme);
        
        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
        }
        
        // تطبيق ألوان السمة
        const themeColors = this.themes[theme]?.colors;
        if (themeColors) {
            Object.entries(themeColors).forEach(([key, value]) => {
                document.documentElement.style.setProperty(`--theme-${key}`, value);
            });
        }
    }
    
    /**
     * تطبيق نظام الألوان
     */
    applyColorScheme() {
        const scheme = this.colorSchemes[this.settings.colorScheme];
        if (scheme) {
            Object.entries(scheme).forEach(([key, value]) => {
                document.documentElement.style.setProperty(`--${key}-color`, value);
            });
        }
    }
    
    /**
     * تطبيق حجم الخط
     */
    applyFontSize() {
        const fontSize = this.fontSizes[this.settings.fontSize];
        if (fontSize) {
            document.documentElement.style.setProperty('--base-font-size', fontSize.base);
            document.documentElement.style.setProperty('--font-scale', fontSize.scale);
        }
    }
    
    /**
     * تطبيق الرسوم المتحركة
     */
    applyAnimations() {
        if (this.settings.animations) {
            document.documentElement.classList.remove('no-animations');
        } else {
            document.documentElement.classList.add('no-animations');
        }
    }
    
    /**
     * تطبيق اللغة
     */
    applyLanguage() {
        document.documentElement.lang = this.settings.language;
        document.documentElement.dir = this.settings.language === 'ar' ? 'rtl' : 'ltr';
    }
    
    /**
     * تحديث إعداد واحد
     */
    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
        this.applySettings();
        
        // إرسال حدث التحديث
        window.dispatchEvent(new CustomEvent('settingsUpdated', {
            detail: { key, value, settings: this.settings }
        }));
    }
    
    /**
     * الحصول على إعداد
     */
    getSetting(key) {
        return this.settings[key];
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    getAllSettings() {
        return { ...this.settings };
    }
    
    /**
     * إعادة تعيين الإعدادات للافتراضية
     */
    resetSettings() {
        this.settings = {
            theme: 'light',
            language: 'ar',
            autoSave: true,
            notifications: true,
            animations: true,
            fontSize: 'medium',
            colorScheme: 'blue',
            layout: 'default',
            searchHistory: true,
            analytics: false
        };
        this.saveSettings();
        this.applySettings();
    }
    
    /**
     * تصدير الإعدادات
     */
    exportSettings() {
        const settingsBlob = new Blob([JSON.stringify(this.settings, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(settingsBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'blog-generator-settings.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    /**
     * استيراد الإعدادات
     */
    importSettings(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const importedSettings = JSON.parse(e.target.result);
                    this.settings = { ...this.settings, ...importedSettings };
                    this.saveSettings();
                    this.applySettings();
                    resolve(this.settings);
                } catch (error) {
                    reject(new Error('ملف الإعدادات غير صالح'));
                }
            };
            
            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }
    
    /**
     * إنشاء واجهة الإعدادات
     */
    createSettingsInterface() {
        const settingsModal = document.createElement('div');
        settingsModal.className = 'modal fade';
        settingsModal.id = 'settingsModal';
        settingsModal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات التطبيق
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="settings-tabs">
                            <ul class="nav nav-tabs" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#appearance-tab">المظهر</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#behavior-tab">السلوك</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#advanced-tab">متقدم</a>
                                </li>
                            </ul>
                            <div class="tab-content mt-3">
                                ${this.createAppearanceTab()}
                                ${this.createBehaviorTab()}
                                ${this.createAdvancedTab()}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="settingsManager.resetSettings()">
                            إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="settingsManager.exportSettings()">
                            تصدير الإعدادات
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            حفظ
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(settingsModal);
        this.bindSettingsEvents();
        
        return settingsModal;
    }
    
    /**
     * إنشاء تبويب المظهر
     */
    createAppearanceTab() {
        return `
            <div class="tab-pane fade show active" id="appearance-tab">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">السمة</label>
                        <select class="form-select" id="themeSetting">
                            ${Object.entries(this.themes).map(([key, theme]) => 
                                `<option value="${key}" ${this.settings.theme === key ? 'selected' : ''}>${theme.name}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">نظام الألوان</label>
                        <select class="form-select" id="colorSchemeSetting">
                            ${Object.keys(this.colorSchemes).map(scheme => 
                                `<option value="${scheme}" ${this.settings.colorScheme === scheme ? 'selected' : ''}>${scheme}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">حجم الخط</label>
                        <select class="form-select" id="fontSizeSetting">
                            ${Object.entries(this.fontSizes).map(([key, size]) => 
                                `<option value="${key}" ${this.settings.fontSize === key ? 'selected' : ''}>${key}</option>`
                            ).join('')}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">الرسوم المتحركة</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="animationsSetting" 
                                   ${this.settings.animations ? 'checked' : ''}>
                            <label class="form-check-label" for="animationsSetting">
                                تفعيل الرسوم المتحركة
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * إنشاء تبويب السلوك
     */
    createBehaviorTab() {
        return `
            <div class="tab-pane fade" id="behavior-tab">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoSaveSetting" 
                                   ${this.settings.autoSave ? 'checked' : ''}>
                            <label class="form-check-label" for="autoSaveSetting">
                                الحفظ التلقائي للبيانات
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="notificationsSetting" 
                                   ${this.settings.notifications ? 'checked' : ''}>
                            <label class="form-check-label" for="notificationsSetting">
                                إظهار الإشعارات
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="searchHistorySetting" 
                                   ${this.settings.searchHistory ? 'checked' : ''}>
                            <label class="form-check-label" for="searchHistorySetting">
                                حفظ تاريخ البحث
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * إنشاء تبويب متقدم
     */
    createAdvancedTab() {
        return `
            <div class="tab-pane fade" id="advanced-tab">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="analyticsSetting" 
                                   ${this.settings.analytics ? 'checked' : ''}>
                            <label class="form-check-label" for="analyticsSetting">
                                تفعيل التحليلات (مجهولة الهوية)
                            </label>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label">استيراد إعدادات</label>
                        <input type="file" class="form-control" id="importSettings" accept=".json">
                    </div>
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            يمكنك تصدير إعداداتك الحالية واستيرادها لاحقاً أو مشاركتها مع آخرين.
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * ربط أحداث الإعدادات
     */
    bindSettingsEvents() {
        // ربط أحداث التغيير
        const settingElements = [
            'themeSetting', 'colorSchemeSetting', 'fontSizeSetting',
            'animationsSetting', 'autoSaveSetting', 'notificationsSetting',
            'searchHistorySetting', 'analyticsSetting'
        ];
        
        settingElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener('change', (e) => {
                    const key = elementId.replace('Setting', '');
                    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
                    this.updateSetting(key, value);
                });
            }
        });
        
        // ربط استيراد الإعدادات
        const importInput = document.getElementById('importSettings');
        if (importInput) {
            importInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.importSettings(file)
                        .then(() => {
                            alert('تم استيراد الإعدادات بنجاح');
                            location.reload();
                        })
                        .catch(error => {
                            alert('خطأ في استيراد الإعدادات: ' + error.message);
                        });
                }
            });
        }
    }
    
    /**
     * إظهار واجهة الإعدادات
     */
    showSettings() {
        let modal = document.getElementById('settingsModal');
        if (!modal) {
            modal = this.createSettingsInterface();
        }
        
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
}

// إنشاء مثيل عام من مدير الإعدادات
window.settingsManager = new SettingsManager();
