/* ===== CSS Variables ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white-color: #ffffff;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--light-color);
    direction: rtl;
    text-align: right;
}

html {
    scroll-behavior: smooth;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    font-family: 'Tajawal', sans-serif;
}

/* ===== Loading Screen ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: var(--white-color);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Header ===== */
.main-header {
    background: var(--white-color);
    box-shadow: var(--shadow-md);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar {
    padding: 1rem 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    text-decoration: none;
}

.brand-icon {
    font-size: 2rem;
    margin-left: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    font-weight: 500;
    color: var(--gray-700) !important;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* ===== Hero Section ===== */
.hero-section {
    background: var(--gradient-hero);
    color: var(--white-color);
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="100" cy="100" r="2" fill="rgba(255,255,255,0.2)" opacity="0.5"><animate attributeName="cy" values="100;900;100" dur="10s" repeatCount="indefinite"/></circle><circle cx="300" cy="200" r="1.5" fill="rgba(255,255,255,0.3)" opacity="0.7"><animate attributeName="cy" values="200;800;200" dur="8s" repeatCount="indefinite"/></circle><circle cx="500" cy="150" r="1" fill="rgba(255,255,255,0.4)" opacity="0.6"><animate attributeName="cy" values="150;850;150" dur="12s" repeatCount="indefinite"/></circle></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.highlight {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.feature-item i {
    font-size: 1.2rem;
    color: #ffd700;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white-color);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-outline-light {
    background: transparent;
    color: var(--white-color);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* ===== Hero Visual ===== */
.hero-visual {
    position: relative;
    z-index: 2;
}

.hero-mockup {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.mockup-screen {
    background: var(--white-color);
    border-radius: var(--radius-2xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-xl);
    transform: perspective(1000px) rotateY(-15deg) rotateX(10deg);
    transition: transform var(--transition-slow);
}

.mockup-screen:hover {
    transform: perspective(1000px) rotateY(-10deg) rotateX(5deg);
}

.mockup-content {
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: 1rem;
}

.mockup-header {
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
}

.mockup-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mockup-line {
    height: 12px;
    background: var(--gray-300);
    border-radius: var(--radius-sm);
    animation: pulse 2s ease-in-out infinite;
}

.mockup-line.short {
    width: 60%;
}

.mockup-line.medium {
    width: 80%;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ===== Features Section ===== */
.features-section {
    padding: 5rem 0;
    background: var(--white-color);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: 3rem;
}

.feature-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    height: 100%;
    border: 1px solid var(--gray-200);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--white-color);
}

.feature-card h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 0;
}

/* ===== Generator Section ===== */
.generator-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.generator-form {
    background: var(--white-color);
    border-radius: var(--radius-2xl);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    max-width: 1000px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section-title i {
    color: var(--primary-color);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all var(--transition-fast);
    background: var(--white-color);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    outline: none;
}

/* ===== Examples Section ===== */
.examples-section {
    padding: 5rem 0;
    background: var(--gray-100);
}

.example-card {
    background: var(--white-color);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    height: 100%;
}

.example-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.example-image {
    padding: 2rem;
    background: var(--gradient-primary);
}

.example-mockup {
    max-width: 250px;
    margin: 0 auto;
}

.mockup-browser {
    background: var(--white-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.browser-header {
    background: var(--gray-200);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
}

.browser-dots {
    display: flex;
    gap: 0.5rem;
}

.browser-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-400);
}

.browser-dots span:nth-child(1) { background: #ff5f56; }
.browser-dots span:nth-child(2) { background: #ffbd2e; }
.browser-dots span:nth-child(3) { background: #27ca3f; }

.browser-content {
    padding: 1rem;
    min-height: 120px;
}

.content-header {
    height: 20px;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
    margin-bottom: 1rem;
}

.content-body {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.content-line {
    height: 8px;
    background: var(--gray-300);
    border-radius: var(--radius-sm);
}

.content-line.short {
    width: 60%;
}

.example-content {
    padding: 1.5rem;
}

.example-content h4 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.example-content p {
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.example-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.example-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* ===== Statistics Section ===== */
.stats-section {
    padding: 5rem 0;
    background: var(--gradient-primary);
    color: var(--white-color);
}

.stat-item {
    text-align: center;
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.stat-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.stat-icon i {
    font-size: 2rem;
    color: var(--white-color);
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* ===== FAQ Section ===== */
.faq-section {
    padding: 5rem 0;
    background: var(--white-color);
}

.accordion-item {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg) !important;
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-button {
    background: var(--white-color);
    color: var(--gray-800);
    font-weight: 600;
    padding: 1.5rem;
    border: none;
    text-align: right;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: var(--white-color);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

.accordion-body {
    padding: 1.5rem;
    background: var(--gray-50);
    color: var(--gray-700);
    line-height: 1.6;
}

/* ===== Footer ===== */
.main-footer {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: 3rem 0 1rem;
}

.footer-brand h4 {
    color: var(--white-color);
    margin-bottom: 1rem;
}

.footer-brand p {
    line-height: 1.6;
    margin-bottom: 0;
}

.footer-links h5 {
    color: var(--white-color);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links ul li a:hover {
    color: var(--white-color);
}

.footer-social h5 {
    color: var(--white-color);
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    color: var(--gray-400);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white-color);
    transform: translateY(-2px);
}

.footer-divider {
    border-color: var(--gray-700);
    margin: 2rem 0 1rem;
}

.footer-copyright {
    color: var(--gray-500);
    margin: 0;
    font-size: 0.9rem;
}

/* ===== Progress Animations ===== */
.progress-bar {
    background: var(--gradient-primary);
    transition: width 0.6s ease;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* ===== Form Enhancements ===== */
.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(37, 99, 235, 0.25);
}

/* ===== Counter Animation ===== */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-number.animate {
    animation: countUp 0.6s ease-out;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-actions {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .mockup-screen {
        transform: none;
    }

    .generator-form {
        padding: 2rem 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .social-links {
        justify-content: center;
    }

    .example-stats {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .generator-form {
        padding: 1.5rem 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .example-image {
        padding: 1rem;
    }

    .footer-brand,
    .footer-links,
    .footer-social {
        text-align: center;
        margin-bottom: 2rem;
    }
}

/* ===== Search Engine Styles ===== */
.search-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.search-input {
    border: none;
    padding: 1rem 1.5rem;
    font-size: 1.1rem;
    flex: 1;
    background: var(--white-color);
}

.search-input:focus {
    outline: none;
    box-shadow: none;
}

.search-btn {
    border: none;
    padding: 1rem 1.5rem;
    background: var(--gradient-primary);
    color: var(--white-color);
    transition: all var(--transition-normal);
}

.search-btn:hover {
    background: var(--gradient-secondary);
    transform: scale(1.05);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--white-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    display: none;
    margin-top: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    border-bottom: 1px solid var(--gray-200);
}

.suggestion-item:hover {
    background: var(--gray-100);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.search-results {
    margin-top: 2rem;
}

.results-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.results-header h5 {
    color: var(--gray-800);
    margin: 0;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.result-item {
    background: var(--white-color);
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.result-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.result-type {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: var(--white-color);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
}

.result-title {
    margin-bottom: 0.75rem;
}

.result-title a {
    color: var(--gray-900);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.result-title a:hover {
    color: var(--primary-color);
}

.result-content {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.result-content mark {
    background: var(--warning-color);
    color: var(--white-color);
    padding: 0.125rem 0.25rem;
    border-radius: var(--radius-sm);
}

.result-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.result-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.no-results i {
    opacity: 0.5;
}

/* ===== Search Responsive ===== */
@media (max-width: 768px) {
    .search-input-group {
        flex-direction: column;
        border-radius: var(--radius-lg);
    }

    .search-input {
        border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    }

    .search-btn {
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }

    .result-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}
