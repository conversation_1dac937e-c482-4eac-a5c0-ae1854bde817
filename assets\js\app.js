/**
 * Main Application - التطبيق الرئيسي
 * يدير واجهة المستخدم والتفاعل مع مولد المدونات
 */

class BlogGeneratorApp {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 6;
        this.formData = {};
        this.generatedBlog = null;
        this.isGenerating = false;

        this.init();
    }

    /**
     * تهيئة التطبيق
     */
    init() {
        this.hideLoadingScreen();
        this.setupEventListeners();
        this.loadDefaultSettings();
        this.initializeForm();
    }

    /**
     * إخفاء شاشة التحميل
     */
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
            }
        }, 1500);
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تفعيل التنقل السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // تفعيل النموذج
        const form = document.getElementById('blogGeneratorForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }

        // تفعيل أزرار التصدير
        this.setupExportButtons();
    }

    /**
     * تحميل الإعدادات الافتراضية
     */
    loadDefaultSettings() {
        this.defaultSettings = {
            blogName: 'مدونة التقنية العربية',
            language: 'ar',
            primaryColors: '#2563eb,#1e40af,#3b82f6',
            complexity: 'متوسط',
            sections: 'تقنية، صحة، تعليم، أعمال، ترفيه',
            keywords: 'الذكاء الاصطناعي، البرمجة، التكنولوجيا، الصحة الرقمية، التعليم الإلكتروني',
            articlesPerSection: 3,
            wordsPerParagraph: 150,
            headingsPerArticle: 4,
            includeTables: true,
            includeLists: true,
            tone: 'احترافي',
            includeImages: true,
            includeSearch: true,
            includePrivacyPages: true,
            heroStyle: 'gradient',
            seoOptimized: true
        };
    }

    /**
     * تهيئة النموذج
     */
    initializeForm() {
        this.createFormSections();
        this.populateDefaultValues();
    }

    /**
     * إنشاء أقسام النموذج
     */
    createFormSections() {
        const form = document.getElementById('blogGeneratorForm');
        if (!form) return;

        // إضافة أقسام النموذج
        this.addDesignSection(form);
        this.addContentSection(form);
        this.addSEOSection(form);
        this.addAdvancedSection(form);
        this.addSubmitSection(form);
    }

    /**
     * إضافة قسم التصميم
     */
    addDesignSection(form) {
        const designSection = document.createElement('div');
        designSection.className = 'form-section';
        designSection.innerHTML = `
            <h3 class="form-section-title">
                <i class="fas fa-palette"></i>
                التصميم والألوان
            </h3>
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="primaryColors" class="form-label">الألوان الأساسية</label>
                    <input type="text" class="form-control" id="primaryColors"
                           placeholder="#2563eb,#1e40af,#3b82f6">
                    <small class="form-text text-muted">أدخل الألوان مفصولة بفاصلة</small>
                </div>
                <div class="col-md-6">
                    <label for="complexity" class="form-label">مستوى التعقيد والجمال</label>
                    <select class="form-select" id="complexity">
                        <option value="بسيط">بسيط</option>
                        <option value="متوسط" selected>متوسط</option>
                        <option value="معقد">معقد ومتقدم</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="heroStyle" class="form-label">نمط الـ Hero</label>
                    <select class="form-select" id="heroStyle">
                        <option value="gradient" selected>تدرج لوني</option>
                        <option value="image">صورة خلفية</option>
                        <option value="animated">متحرك</option>
                        <option value="minimal">بسيط</option>
                        <option value="disabled">معطل</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="includeImages" class="form-label">تضمين الصور</label>
                    <select class="form-select" id="includeImages">
                        <option value="true" selected>نعم - صور حقيقية</option>
                        <option value="icons">أيقونات معبرة</option>
                        <option value="false">لا</option>
                    </select>
                </div>
            </div>
        `;
        form.appendChild(designSection);
    }

    /**
     * إضافة قسم المحتوى
     */
    addContentSection(form) {
        const contentSection = document.createElement('div');
        contentSection.className = 'form-section';
        contentSection.innerHTML = `
            <h3 class="form-section-title">
                <i class="fas fa-edit"></i>
                المحتوى والمقالات
            </h3>
            <div class="row g-3">
                <div class="col-12">
                    <label for="sections" class="form-label">أقسام المدونة</label>
                    <textarea class="form-control" id="sections" rows="3"
                              placeholder="تقنية، صحة، تعليم، أعمال، ترفيه"></textarea>
                    <small class="form-text text-muted">أدخل الأقسام مفصولة بفاصلة</small>
                </div>
                <div class="col-12">
                    <label for="keywords" class="form-label">الكلمات المفتاحية أو عناوين المقالات</label>
                    <textarea class="form-control" id="keywords" rows="4"
                              placeholder="الذكاء الاصطناعي، البرمجة، التكنولوجيا..."></textarea>
                    <small class="form-text text-muted">أدخل الكلمات المفتاحية أو العناوين مفصولة بفاصلة أو سطر جديد</small>
                </div>
                <div class="col-md-4">
                    <label for="articlesPerSection" class="form-label">عدد المقالات لكل قسم</label>
                    <input type="number" class="form-control" id="articlesPerSection" value="3" min="1" max="10">
                </div>
                <div class="col-md-4">
                    <label for="wordsPerParagraph" class="form-label">عدد الكلمات لكل فقرة</label>
                    <input type="number" class="form-control" id="wordsPerParagraph" value="150" min="50" max="300">
                </div>
                <div class="col-md-4">
                    <label for="headingsPerArticle" class="form-label">عدد العناوين لكل مقال</label>
                    <input type="number" class="form-control" id="headingsPerArticle" value="4" min="2" max="8">
                </div>
                <div class="col-md-4">
                    <label for="tone" class="form-label">نبرة ولهجة المقال</label>
                    <select class="form-select" id="tone">
                        <option value="احترافي" selected>احترافي</option>
                        <option value="ودود">ودود</option>
                        <option value="تعليمي">تعليمي</option>
                        <option value="تحفيزي">تحفيزي</option>
                        <option value="علمي">علمي</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">تضمين الجداول</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeTables" checked>
                        <label class="form-check-label" for="includeTables">
                            تضمين جداول مع تعريفها
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">تضمين القوائم</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeLists" checked>
                        <label class="form-check-label" for="includeLists">
                            تضمين قوائم نقطية مع تعريفها
                        </label>
                    </div>
                </div>
            </div>
        `;
        form.appendChild(contentSection);
    }

    /**
     * إضافة قسم السيو
     */
    addSEOSection(form) {
        const seoSection = document.createElement('div');
        seoSection.className = 'form-section';
        seoSection.innerHTML = `
            <h3 class="form-section-title">
                <i class="fas fa-search"></i>
                تحسين محركات البحث (SEO)
            </h3>
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">التهيئة الكاملة للسيو</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="seoOptimized" checked>
                        <label class="form-check-label" for="seoOptimized">
                            تفعيل التحسين الكامل للسيو والاسكيما
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <label class="form-label">محرك البحث الداخلي</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeSearch" checked>
                        <label class="form-check-label" for="includeSearch">
                            تضمين محرك بحث احترافي
                        </label>
                    </div>
                </div>
                <div class="col-12">
                    <label for="faviconUrl" class="form-label">رابط أيقونة الفافيكون (اختياري)</label>
                    <input type="url" class="form-control" id="faviconUrl"
                           placeholder="https://example.com/favicon.ico">
                </div>
                <div class="col-12">
                    <label for="logoUrl" class="form-label">رابط صورة اللوجو (اختياري)</label>
                    <input type="url" class="form-control" id="logoUrl"
                           placeholder="https://example.com/logo.png">
                </div>
            </div>
        `;
        form.appendChild(seoSection);
    }

    /**
     * إضافة قسم الإعدادات المتقدمة
     */
    addAdvancedSection(form) {
        const advancedSection = document.createElement('div');
        advancedSection.className = 'form-section';
        advancedSection.innerHTML = `
            <h3 class="form-section-title">
                <i class="fas fa-cogs"></i>
                إعدادات متقدمة
            </h3>
            <div class="row g-3">
                <div class="col-12">
                    <label class="form-label">الصفحات الإضافية</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includePrivacyPages" checked>
                        <label class="form-check-label" for="includePrivacyPages">
                            تضمين صفحات الخصوصية، من نحن، شروط الاستخدام، اتصل بنا
                        </label>
                    </div>
                </div>
                <div class="col-12">
                    <label for="additionalPrompt" class="form-label">برومبت إضافي (اختياري)</label>
                    <textarea class="form-control" id="additionalPrompt" rows="3"
                              placeholder="أضف تعليمات إضافية أو متطلبات خاصة..."></textarea>
                </div>
                <div class="col-12">
                    <label for="heroSections" class="form-label">أقسام الصفحة الرئيسية</label>
                    <textarea class="form-control" id="heroSections" rows="4"
                              placeholder="من نحن&#10;خدماتنا&#10;مميزاتنا&#10;اتصل بنا"></textarea>
                    <small class="form-text text-muted">كل قسم في سطر منفصل</small>
                </div>
            </div>
        `;
        form.appendChild(advancedSection);
    }

    /**
     * إضافة قسم الإرسال
     */
    addSubmitSection(form) {
        const submitSection = document.createElement('div');
        submitSection.className = 'form-section text-center';
        submitSection.innerHTML = `
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <button type="button" class="btn btn-outline-secondary btn-lg" onclick="app.resetForm()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                    <i class="fas fa-magic"></i>
                    إنشاء المدونة
                </button>
            </div>

            <!-- Progress Section -->
            <div id="progressSection" class="mt-4" style="display: none;">
                <div class="progress mb-3" style="height: 25px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%"></div>
                </div>
                <div id="progressText" class="text-muted">جاري التحضير...</div>
                <button type="button" class="btn btn-warning btn-sm mt-2" onclick="app.pauseGeneration()">
                    <i class="fas fa-pause"></i>
                    إيقاف مؤقت
                </button>
            </div>

            <!-- Results Section -->
            <div id="resultsSection" class="mt-5" style="display: none;">
                <h3 class="mb-4">تم إنشاء المدونة بنجاح!</h3>
                <div class="row g-3">
                    <div class="col-md-6">
                        <button type="button" class="btn btn-success w-100" onclick="app.previewBlog()">
                            <i class="fas fa-eye"></i>
                            معاينة المدونة
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="dropdown">
                            <button class="btn btn-info dropdown-toggle w-100" type="button"
                                    data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i>
                                تصدير المدونة
                            </button>
                            <ul class="dropdown-menu w-100">
                                <li><a class="dropdown-item" href="#" onclick="app.exportAs('html')">
                                    <i class="fab fa-html5"></i> ملف HTML
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="app.exportAs('pdf')">
                                    <i class="fas fa-file-pdf"></i> ملف PDF
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="app.exportAs('excel')">
                                    <i class="fas fa-file-excel"></i> ملف Excel
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="app.exportAs('text')">
                                    <i class="fas fa-file-alt"></i> ملف نصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="app.exportAs('zip')">
                                    <i class="fas fa-file-archive"></i> ملف مضغوط (كامل)
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
        form.appendChild(submitSection);
    }

    /**
     * ملء القيم الافتراضية
     */
    populateDefaultValues() {
        Object.keys(this.defaultSettings).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = this.defaultSettings[key];
                } else {
                    element.value = this.defaultSettings[key];
                }
            }
        });
    }

    /**
     * معالجة إرسال النموذج
     */
    async handleFormSubmit() {
        if (this.isGenerating) return;

        try {
            this.collectFormData();
            this.validateFormData();
            await this.generateBlog();
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * جمع بيانات النموذج
     */
    collectFormData() {
        const formElements = document.querySelectorAll('#blogGeneratorForm input, #blogGeneratorForm select, #blogGeneratorForm textarea');
        this.formData = {};

        formElements.forEach(element => {
            if (element.type === 'checkbox') {
                this.formData[element.id] = element.checked;
            } else {
                this.formData[element.id] = element.value;
            }
        });
    }

    /**
     * التحقق من صحة البيانات
     */
    validateFormData() {
        if (!this.formData.blogName || this.formData.blogName.trim().length < 3) {
            throw new Error('يجب أن يكون اسم المدونة 3 أحرف على الأقل');
        }

        if (!this.formData.sections || this.formData.sections.trim().length === 0) {
            throw new Error('يجب تحديد أقسام المدونة');
        }
    }

    /**
     * إنشاء المدونة
     */
    async generateBlog() {
        this.isGenerating = true;
        this.showProgress();

        // إشعار بدء التوليد
        const loadingNotification = window.notificationManager.loading(
            'جاري إنشاء مدونتك الاحترافية...',
            'بدء التوليد'
        );

        try {
            this.generatedBlog = await window.blogGenerator.generateBlog(
                this.formData,
                (progress, message) => this.updateProgress(progress, message)
            );

            window.exportManager.setBlog(this.generatedBlog);

            // إخفاء إشعار التحميل وإظهار النجاح
            window.notificationManager.hide(loadingNotification);
            this.showSuccess('تم إنشاء مدونتك بنجاح! يمكنك الآن معاينتها أو تصديرها.');

            this.showResults();

        } catch (error) {
            // إخفاء إشعار التحميل وإظهار الخطأ
            window.notificationManager.hide(loadingNotification);
            this.showError(`فشل في إنشاء المدونة: ${error.message}`);
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * عرض شريط التقدم
     */
    showProgress() {
        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('generateBtn').disabled = true;
    }

    /**
     * تحديث شريط التقدم
     */
    updateProgress(percentage, message) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
        }

        if (progressText) {
            progressText.textContent = message;
        }
    }

    /**
     * عرض النتائج
     */
    showResults() {
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('generateBtn').disabled = false;
        this.updateProgress(100, 'تم إنشاء المدونة بنجاح!');
    }

    /**
     * عرض خطأ
     */
    showError(message) {
        window.notificationManager.error(message, 'خطأ في التوليد');
        document.getElementById('generateBtn').disabled = false;
        document.getElementById('progressSection').style.display = 'none';
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        window.notificationManager.success(message, 'تم بنجاح!');
    }

    /**
     * عرض تحذير
     */
    showWarning(message) {
        window.notificationManager.warning(message, 'تحذير');
    }

    /**
     * عرض معلومة
     */
    showInfo(message) {
        window.notificationManager.info(message, 'معلومة');
    }

    /**
     * معاينة المدونة
     */
    previewBlog() {
        if (!this.generatedBlog) return;

        const htmlContent = window.exportManager.generateCompleteHTML();
        const newWindow = window.open('', '_blank');
        newWindow.document.write(htmlContent);
        newWindow.document.close();
    }

    /**
     * تصدير المدونة
     */
    async exportAs(format) {
        if (!this.generatedBlog) {
            this.showWarning('لا توجد مدونة للتصدير. يرجى إنشاء مدونة أولاً.');
            return;
        }

        const formatNames = {
            html: 'HTML',
            pdf: 'PDF',
            excel: 'Excel',
            text: 'نص',
            zip: 'ملف مضغوط'
        };

        const loadingNotification = window.notificationManager.loading(
            `جاري تصدير المدونة بصيغة ${formatNames[format]}...`,
            'تصدير المدونة'
        );

        try {
            switch (format) {
                case 'html':
                    await window.exportManager.exportAsHTML();
                    break;
                case 'pdf':
                    await window.exportManager.exportAsPDF();
                    break;
                case 'excel':
                    await window.exportManager.exportAsExcel();
                    break;
                case 'text':
                    await window.exportManager.exportAsText();
                    break;
                case 'zip':
                    await window.exportManager.exportAsZip();
                    break;
            }

            window.notificationManager.hide(loadingNotification);
            this.showSuccess(`تم تصدير المدونة بصيغة ${formatNames[format]} بنجاح!`);

        } catch (error) {
            window.notificationManager.hide(loadingNotification);
            this.showError(`فشل في التصدير بصيغة ${formatNames[format]}: ${error.message}`);
        }
    }

    /**
     * إعادة تعيين النموذج
     */
    resetForm() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
            document.getElementById('blogGeneratorForm').reset();
            this.populateDefaultValues();
            document.getElementById('progressSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            this.generatedBlog = null;
        }
    }

    /**
     * إيقاف التوليد مؤقتاً
     */
    pauseGeneration() {
        // يمكن تطوير هذه الوظيفة لاحقاً
        alert('سيتم تطوير هذه الميزة قريباً');
    }

    /**
     * إعداد أزرار التصدير
     */
    setupExportButtons() {
        // سيتم إضافة المزيد من الوظائف هنا
    }
}

    /**
     * تحريك الأرقام في قسم الإحصائيات
     */
    animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.getAttribute('data-count'));
                    const duration = 2000; // 2 ثانية
                    const step = target / (duration / 16); // 60 FPS
                    let current = 0;

                    const timer = setInterval(() => {
                        current += step;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        counter.textContent = Math.floor(current).toLocaleString('ar-SA');
                        counter.classList.add('animate');
                    }, 16);

                    observer.unobserve(counter);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }

    /**
     * إضافة تأثيرات التمرير
     */
    addScrollEffects() {
        // زر العودة للأعلى
        const backToTopBtn = document.createElement('button');
        backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
        backToTopBtn.className = 'btn btn-primary position-fixed bottom-0 end-0 m-3';
        backToTopBtn.style.display = 'none';
        backToTopBtn.style.zIndex = '1000';
        backToTopBtn.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
        document.body.appendChild(backToTopBtn);

        // إظهار/إخفاء زر العودة للأعلى
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });

        // تأثير الشفافية للهيدر
        const header = document.querySelector('.main-header');
        if (header) {
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 100) {
                    header.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
                    header.style.backdropFilter = 'blur(10px)';
                } else {
                    header.style.backgroundColor = 'rgba(255, 255, 255, 1)';
                    header.style.backdropFilter = 'none';
                }
            });
        }
    }

    /**
     * تفعيل التحقق من صحة النموذج
     */
    enableFormValidation() {
        const form = document.getElementById('blogGeneratorForm');
        if (!form) return;

        // التحقق الفوري من الحقول
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                this.validateField(input);
            });

            input.addEventListener('input', () => {
                if (input.classList.contains('is-invalid')) {
                    this.validateField(input);
                }
            });
        });
    }

    /**
     * التحقق من صحة حقل واحد
     */
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'هذا الحقل مطلوب';
        }

        // التحقق من طول النص
        if (field.id === 'blogName' && value.length > 0 && value.length < 3) {
            isValid = false;
            errorMessage = 'يجب أن يكون اسم المدونة 3 أحرف على الأقل';
        }

        // التحقق من الأرقام
        if (field.type === 'number') {
            const num = parseInt(value);
            const min = parseInt(field.getAttribute('min'));
            const max = parseInt(field.getAttribute('max'));

            if (value && (isNaN(num) || num < min || num > max)) {
                isValid = false;
                errorMessage = `يجب أن يكون الرقم بين ${min} و ${max}`;
            }
        }

        // التحقق من الروابط
        if (field.type === 'url' && value) {
            try {
                new URL(value);
            } catch {
                isValid = false;
                errorMessage = 'يرجى إدخال رابط صحيح';
            }
        }

        // تطبيق النتيجة
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
            this.removeFieldError(field);
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * عرض خطأ الحقل
     */
    showFieldError(field, message) {
        this.removeFieldError(field);

        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        errorDiv.id = `${field.id}-error`;

        field.parentNode.appendChild(errorDiv);
    }

    /**
     * إزالة خطأ الحقل
     */
    removeFieldError(field) {
        const existingError = document.getElementById(`${field.id}-error`);
        if (existingError) {
            existingError.remove();
        }
    }

    /**
     * حفظ البيانات محلياً
     */
    saveFormDataLocally() {
        const formData = {};
        const form = document.getElementById('blogGeneratorForm');
        if (!form) return;

        const elements = form.querySelectorAll('input, select, textarea');
        elements.forEach(element => {
            if (element.type === 'checkbox') {
                formData[element.id] = element.checked;
            } else {
                formData[element.id] = element.value;
            }
        });

        localStorage.setItem('blogGeneratorFormData', JSON.stringify(formData));
    }

    /**
     * استرداد البيانات المحفوظة محلياً
     */
    loadFormDataLocally() {
        const savedData = localStorage.getItem('blogGeneratorFormData');
        if (!savedData) return;

        try {
            const formData = JSON.parse(savedData);
            Object.keys(formData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = formData[key];
                    } else {
                        element.value = formData[key];
                    }
                }
            });
        } catch (error) {
            console.warn('فشل في استرداد البيانات المحفوظة:', error);
        }
    }

    /**
     * مسح البيانات المحفوظة محلياً
     */
    clearLocalFormData() {
        localStorage.removeItem('blogGeneratorFormData');
    }

    /**
     * إضافة اختصارات لوحة المفاتيح
     */
    addKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter لإرسال النموذج
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                const generateBtn = document.getElementById('generateBtn');
                if (generateBtn && !generateBtn.disabled) {
                    generateBtn.click();
                }
            }

            // Ctrl/Cmd + R لإعادة تعيين النموذج
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                this.resetForm();
            }

            // Escape لإلغاء التوليد
            if (e.key === 'Escape' && this.isGenerating) {
                this.pauseGeneration();
            }
        });
    }

    /**
     * إضافة نصائح مساعدة
     */
    addHelpTooltips() {
        const tooltips = {
            'blogName': 'اختر اسماً جذاباً ومميزاً لمدونتك',
            'primaryColors': 'أدخل الألوان بصيغة HEX مفصولة بفاصلة',
            'sections': 'أدخل أسماء الأقسام مفصولة بفاصلة',
            'keywords': 'أدخل الكلمات المفتاحية أو عناوين المقالات',
            'additionalPrompt': 'أضف تعليمات إضافية للذكاء الاصطناعي'
        };

        Object.keys(tooltips).forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.setAttribute('title', tooltips[fieldId]);
                field.setAttribute('data-bs-toggle', 'tooltip');
            }
        });

        // تفعيل Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

// دوال مساعدة عامة
function scrollToGenerator() {
    document.getElementById('generator').scrollIntoView({ behavior: 'smooth' });
}

function showDemo() {
    // إنشاء مودال للعرض التوضيحي
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">العرض التوضيحي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="ratio ratio-16x9">
                        <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    window.app = new BlogGeneratorApp();

    // تفعيل الميزات الإضافية
    setTimeout(() => {
        window.app.animateCounters();
        window.app.addScrollEffects();
        window.app.enableFormValidation();
        window.app.addKeyboardShortcuts();
        window.app.addHelpTooltips();
        window.app.loadFormDataLocally();

        // حفظ البيانات تلقائياً كل 30 ثانية
        setInterval(() => {
            if (!window.app.isGenerating) {
                window.app.saveFormDataLocally();
            }
        }, 30000);
    }, 1000);
});
