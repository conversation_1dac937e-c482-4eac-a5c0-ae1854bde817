/**
 * Icon Manager - مدير الأيقونات والصور
 * يدير الأيقونات والصور المستخدمة في المدونة
 */

class IconManager {
    constructor() {
        this.iconSets = {
            technology: ['laptop-code', 'microchip', 'robot', 'wifi', 'mobile-alt', 'desktop', 'code', 'database'],
            health: ['heartbeat', 'user-md', 'pills', 'stethoscope', 'hospital', 'first-aid', 'syringe', 'dna'],
            education: ['graduation-cap', 'book', 'chalkboard-teacher', 'university', 'pencil-alt', 'brain', 'lightbulb', 'certificate'],
            business: ['briefcase', 'chart-line', 'handshake', 'building', 'coins', 'calculator', 'presentation', 'target'],
            entertainment: ['film', 'music', 'gamepad', 'camera', 'theater-masks', 'tv', 'headphones', 'ticket-alt'],
            sports: ['football-ball', 'basketball-ball', 'running', 'swimmer', 'bicycle', 'trophy', 'medal', 'stopwatch'],
            food: ['utensils', 'pizza-slice', 'hamburger', 'coffee', 'wine-glass', 'apple-alt', 'carrot', 'fish'],
            travel: ['plane', 'car', 'map', 'compass', 'suitcase', 'camera', 'mountain', 'globe'],
            science: ['atom', 'flask', 'microscope', 'dna', 'satellite', 'rocket', 'telescope', 'magnet'],
            art: ['palette', 'paint-brush', 'image', 'camera', 'music', 'theater-masks', 'pen-fancy', 'drafting-compass']
        };
        
        this.colorPalettes = {
            technology: ['#3b82f6', '#1e40af', '#6366f1', '#8b5cf6'],
            health: ['#10b981', '#059669', '#34d399', '#6ee7b7'],
            education: ['#f59e0b', '#d97706', '#fbbf24', '#fcd34d'],
            business: ['#ef4444', '#dc2626', '#f87171', '#fca5a5'],
            entertainment: ['#8b5cf6', '#7c3aed', '#a78bfa', '#c4b5fd'],
            sports: ['#06b6d4', '#0891b2', '#22d3ee', '#67e8f9'],
            food: ['#f97316', '#ea580c', '#fb923c', '#fdba74'],
            travel: ['#84cc16', '#65a30d', '#a3e635', '#bef264'],
            science: ['#6366f1', '#4f46e5', '#818cf8', '#a5b4fc'],
            art: ['#ec4899', '#db2777', '#f472b6', '#f9a8d4']
        };
        
        this.defaultIcons = {
            blog: 'blog',
            article: 'file-alt',
            section: 'folder',
            search: 'search',
            home: 'home',
            about: 'info-circle',
            contact: 'envelope',
            privacy: 'shield-alt',
            terms: 'file-contract'
        };
    }
    
    /**
     * الحصول على أيقونة مناسبة للموضوع
     */
    getIconForTopic(topic, category = null) {
        const topicLower = topic.toLowerCase();
        
        // البحث في الفئات المحددة
        if (category && this.iconSets[category]) {
            return this.getRandomIcon(this.iconSets[category]);
        }
        
        // البحث التلقائي حسب الكلمات المفتاحية
        for (const [cat, icons] of Object.entries(this.iconSets)) {
            if (this.isTopicRelatedToCategory(topicLower, cat)) {
                return this.getRandomIcon(icons);
            }
        }
        
        // أيقونة افتراضية
        return this.defaultIcons.article;
    }
    
    /**
     * فحص ارتباط الموضوع بالفئة
     */
    isTopicRelatedToCategory(topic, category) {
        const keywords = {
            technology: ['تقنية', 'تكنولوجيا', 'برمجة', 'حاسوب', 'ذكاء', 'تطبيق', 'موقع', 'انترنت', 'رقمي'],
            health: ['صحة', 'طب', 'علاج', 'دواء', 'مرض', 'طبيب', 'مستشفى', 'صحي', 'طبي'],
            education: ['تعليم', 'تعلم', 'دراسة', 'مدرسة', 'جامعة', 'تدريب', 'كورس', 'تعليمي', 'أكاديمي'],
            business: ['أعمال', 'شركة', 'تجارة', 'استثمار', 'مال', 'اقتصاد', 'إدارة', 'تسويق', 'مبيعات'],
            entertainment: ['ترفيه', 'لعبة', 'فيلم', 'موسيقى', 'مسرح', 'فن', 'تسلية', 'متعة'],
            sports: ['رياضة', 'كرة', 'لعب', 'تمرين', 'لياقة', 'بطولة', 'فريق', 'مباراة'],
            food: ['طعام', 'أكل', 'طبخ', 'وصفة', 'مطبخ', 'غذاء', 'مطعم', 'شراب'],
            travel: ['سفر', 'سياحة', 'رحلة', 'طيران', 'فندق', 'مكان', 'بلد', 'مدينة'],
            science: ['علم', 'بحث', 'تجربة', 'اكتشاف', 'فيزياء', 'كيمياء', 'أحياء', 'رياضيات'],
            art: ['فن', 'رسم', 'تصميم', 'إبداع', 'لوحة', 'نحت', 'جمال', 'ثقافة']
        };
        
        const categoryKeywords = keywords[category] || [];
        return categoryKeywords.some(keyword => topic.includes(keyword));
    }
    
    /**
     * الحصول على أيقونة عشوائية من مجموعة
     */
    getRandomIcon(iconArray) {
        return iconArray[Math.floor(Math.random() * iconArray.length)];
    }
    
    /**
     * الحصول على لون مناسب للفئة
     */
    getColorForCategory(category) {
        const colors = this.colorPalettes[category];
        return colors ? this.getRandomColor(colors) : '#6b7280';
    }
    
    /**
     * الحصول على لون عشوائي من مجموعة
     */
    getRandomColor(colorArray) {
        return colorArray[Math.floor(Math.random() * colorArray.length)];
    }
    
    /**
     * إنشاء أيقونة SVG مخصصة
     */
    createCustomIcon(type, color = '#6b7280', size = 24) {
        const icons = {
            blog: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </svg>`,
            
            article: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}">
                <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
            </svg>`,
            
            search: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>`,
            
            star: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>`,
            
            heart: `<svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="${color}">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
            </svg>`
        };
        
        return icons[type] || icons.article;
    }
    
    /**
     * إنشاء صورة placeholder
     */
    createPlaceholderImage(width = 400, height = 300, text = 'صورة', backgroundColor = '#e5e7eb', textColor = '#6b7280') {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        
        // الخلفية
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
        
        // النص
        ctx.fillStyle = textColor;
        ctx.font = `${Math.min(width, height) / 8}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, width / 2, height / 2);
        
        return canvas.toDataURL();
    }
    
    /**
     * إنشاء أيقونة متحركة
     */
    createAnimatedIcon(type, color = '#6b7280') {
        const animations = {
            loading: `
                <div class="spinner-border text-primary" role="status" style="color: ${color} !important;">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
            `,
            
            success: `
                <div class="text-success" style="color: ${color} !important;">
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            `,
            
            error: `
                <div class="text-danger" style="color: ${color} !important;">
                    <i class="fas fa-exclamation-circle fa-2x"></i>
                </div>
            `,
            
            pulse: `
                <div class="pulse-icon" style="color: ${color};">
                    <i class="fas fa-heart fa-2x"></i>
                </div>
                <style>
                .pulse-icon {
                    animation: pulse 1.5s ease-in-out infinite;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }
                </style>
            `
        };
        
        return animations[type] || animations.loading;
    }
    
    /**
     * الحصول على أيقونة للقسم
     */
    getSectionIcon(sectionName) {
        const sectionLower = sectionName.toLowerCase();
        
        // خريطة الأيقونات المخصصة للأقسام
        const sectionIcons = {
            'تقنية': 'laptop-code',
            'تكنولوجيا': 'microchip',
            'صحة': 'heartbeat',
            'طب': 'user-md',
            'تعليم': 'graduation-cap',
            'تعلم': 'book',
            'أعمال': 'briefcase',
            'تجارة': 'chart-line',
            'ترفيه': 'film',
            'رياضة': 'football-ball',
            'طعام': 'utensils',
            'سفر': 'plane',
            'علم': 'flask',
            'فن': 'palette'
        };
        
        // البحث المباشر
        for (const [name, icon] of Object.entries(sectionIcons)) {
            if (sectionLower.includes(name)) {
                return icon;
            }
        }
        
        // البحث في الفئات
        return this.getIconForTopic(sectionName);
    }
    
    /**
     * إنشاء مجموعة أيقونات للمدونة
     */
    generateBlogIconSet(blogData) {
        const iconSet = {
            blog: this.defaultIcons.blog,
            sections: {},
            articles: {},
            pages: {
                about: this.defaultIcons.about,
                contact: this.defaultIcons.contact,
                privacy: this.defaultIcons.privacy,
                terms: this.defaultIcons.terms
            }
        };
        
        // أيقونات الأقسام
        if (blogData.sections) {
            blogData.sections.forEach(section => {
                iconSet.sections[section.id] = this.getSectionIcon(section.name);
            });
        }
        
        // أيقونات المقالات
        if (blogData.articles) {
            blogData.articles.forEach(article => {
                iconSet.articles[article.id] = this.getIconForTopic(article.title, article.category);
            });
        }
        
        return iconSet;
    }
    
    /**
     * تطبيق الأيقونات على العناصر
     */
    applyIconsToElements(iconSet) {
        // تطبيق أيقونات الأقسام
        Object.keys(iconSet.sections).forEach(sectionId => {
            const elements = document.querySelectorAll(`[data-section="${sectionId}"] .section-icon`);
            elements.forEach(element => {
                element.className = `fas fa-${iconSet.sections[sectionId]} section-icon`;
            });
        });
        
        // تطبيق أيقونات المقالات
        Object.keys(iconSet.articles).forEach(articleId => {
            const elements = document.querySelectorAll(`[data-article="${articleId}"] .article-icon`);
            elements.forEach(element => {
                element.className = `fas fa-${iconSet.articles[articleId]} article-icon`;
            });
        });
    }
}

// إنشاء مثيل عام من مدير الأيقونات
window.iconManager = new IconManager();
