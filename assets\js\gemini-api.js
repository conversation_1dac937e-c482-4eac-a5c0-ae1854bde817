/**
 * Gemini API Manager
 * إدارة متقدمة لـ Gemini API مع تدوير المفاتيح وإدارة الحدود
 */

class GeminiAPIManager {
    constructor() {
        // مفاتيح Gemini API
        this.apiKeys = [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ];
        
        this.currentKeyIndex = 0;
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 ثانية
        this.requestQueue = [];
        this.isProcessing = false;
        
        // إحصائيات الاستخدام
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            keyUsage: {}
        };
        
        this.initializeKeyStats();
    }
    
    /**
     * تهيئة إحصائيات المفاتيح
     */
    initializeKeyStats() {
        this.apiKeys.forEach((key, index) => {
            this.stats.keyUsage[index] = {
                requests: 0,
                errors: 0,
                lastUsed: null
            };
        });
    }
    
    /**
     * الحصول على المفتاح الحالي
     */
    getCurrentKey() {
        return this.apiKeys[this.currentKeyIndex];
    }
    
    /**
     * التبديل إلى المفتاح التالي
     */
    rotateKey() {
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        console.log(`تم التبديل إلى المفتاح رقم: ${this.currentKeyIndex + 1}`);
    }
    
    /**
     * تقسيم النص الطويل إلى أجزاء أصغر
     */
    splitLongText(text, maxLength = 30000) {
        if (text.length <= maxLength) {
            return [text];
        }
        
        const chunks = [];
        let currentChunk = '';
        const sentences = text.split(/[.!?؟।]\s+/);
        
        for (const sentence of sentences) {
            if ((currentChunk + sentence).length > maxLength) {
                if (currentChunk) {
                    chunks.push(currentChunk.trim());
                    currentChunk = sentence;
                } else {
                    // الجملة طويلة جداً، قسمها بالقوة
                    const words = sentence.split(' ');
                    let wordChunk = '';
                    for (const word of words) {
                        if ((wordChunk + word).length > maxLength) {
                            if (wordChunk) {
                                chunks.push(wordChunk.trim());
                                wordChunk = word;
                            } else {
                                chunks.push(word);
                            }
                        } else {
                            wordChunk += (wordChunk ? ' ' : '') + word;
                        }
                    }
                    if (wordChunk) {
                        currentChunk = wordChunk;
                    }
                }
            } else {
                currentChunk += (currentChunk ? '. ' : '') + sentence;
            }
        }
        
        if (currentChunk) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }
    
    /**
     * إرسال طلب إلى Gemini API
     */
    async makeRequest(prompt, retryCount = 0) {
        const currentKey = this.getCurrentKey();
        const url = `${this.baseURL}?key=${currentKey}`;
        
        try {
            this.stats.totalRequests++;
            this.stats.keyUsage[this.currentKeyIndex].requests++;
            this.stats.keyUsage[this.currentKeyIndex].lastUsed = new Date();
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 8192,
                    }
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                this.stats.successfulRequests++;
                return data.candidates[0].content.parts[0].text;
            } else {
                throw new Error('استجابة غير صالحة من API');
            }
            
        } catch (error) {
            console.error(`خطأ في الطلب مع المفتاح ${this.currentKeyIndex + 1}:`, error);
            this.stats.failedRequests++;
            this.stats.keyUsage[this.currentKeyIndex].errors++;
            
            // إذا كان الخطأ متعلق بالحد الأقصى، جرب المفتاح التالي
            if (error.message.includes('quota') || error.message.includes('limit') || retryCount < this.maxRetries) {
                this.rotateKey();
                
                if (retryCount < this.maxRetries) {
                    await this.delay(this.retryDelay * (retryCount + 1));
                    return this.makeRequest(prompt, retryCount + 1);
                }
            }
            
            throw error;
        }
    }
    
    /**
     * معالجة النصوص الطويلة بتقسيمها
     */
    async processLongText(prompt) {
        const chunks = this.splitLongText(prompt);
        const results = [];
        
        for (let i = 0; i < chunks.length; i++) {
            try {
                const result = await this.makeRequest(chunks[i]);
                results.push(result);
                
                // تأخير قصير بين الطلبات لتجنب تجاوز الحدود
                if (i < chunks.length - 1) {
                    await this.delay(500);
                }
            } catch (error) {
                console.error(`خطأ في معالجة الجزء ${i + 1}:`, error);
                results.push(`خطأ في معالجة الجزء ${i + 1}`);
            }
        }
        
        return results.join('\n\n');
    }
    
    /**
     * إضافة طلب إلى قائمة الانتظار
     */
    async queueRequest(prompt, priority = 'normal') {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                prompt,
                priority,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processQueue();
        });
    }
    
    /**
     * معالجة قائمة انتظار الطلبات
     */
    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        // ترتيب الطلبات حسب الأولوية
        this.requestQueue.sort((a, b) => {
            const priorityOrder = { high: 3, normal: 2, low: 1 };
            return priorityOrder[b.priority] - priorityOrder[a.priority];
        });
        
        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.processLongText(request.prompt);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }
            
            // تأخير بين الطلبات
            await this.delay(300);
        }
        
        this.isProcessing = false;
    }
    
    /**
     * تأخير لفترة محددة
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * الحصول على إحصائيات الاستخدام
     */
    getStats() {
        return {
            ...this.stats,
            currentKey: this.currentKeyIndex + 1,
            totalKeys: this.apiKeys.length,
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
        };
    }
    
    /**
     * إعادة تعيين الإحصائيات
     */
    resetStats() {
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            keyUsage: {}
        };
        this.initializeKeyStats();
    }
    
    /**
     * اختبار جميع المفاتيح
     */
    async testAllKeys() {
        const results = [];
        const testPrompt = "اكتب كلمة واحدة: مرحبا";
        
        for (let i = 0; i < this.apiKeys.length; i++) {
            this.currentKeyIndex = i;
            try {
                const result = await this.makeRequest(testPrompt);
                results.push({
                    keyIndex: i + 1,
                    status: 'يعمل',
                    response: result.substring(0, 50) + '...'
                });
            } catch (error) {
                results.push({
                    keyIndex: i + 1,
                    status: 'خطأ',
                    error: error.message
                });
            }
        }
        
        this.currentKeyIndex = 0; // إعادة تعيين إلى المفتاح الأول
        return results;
    }
}

// إنشاء مثيل عام من مدير API
window.geminiAPI = new GeminiAPIManager();
