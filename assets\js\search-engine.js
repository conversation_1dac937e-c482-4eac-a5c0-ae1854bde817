/**
 * Search Engine - محرك البحث المتقدم
 * محرك بحث احترافي للمدونات المولدة
 */

class SearchEngine {
    constructor() {
        this.searchIndex = new Map();
        this.searchHistory = [];
        this.searchSuggestions = [];
        this.isIndexed = false;
        this.searchOptions = {
            fuzzySearch: true,
            highlightResults: true,
            maxResults: 20,
            minQueryLength: 2
        };
    }
    
    /**
     * فهرسة المحتوى للبحث
     */
    indexContent(blogData) {
        this.searchIndex.clear();
        
        // فهرسة المقالات
        if (blogData.articles) {
            blogData.articles.forEach(article => {
                this.indexItem({
                    id: article.id,
                    type: 'article',
                    title: article.title,
                    content: this.stripHTML(article.content),
                    excerpt: article.excerpt,
                    keywords: article.keywords,
                    url: `articles/${article.id}.html`,
                    section: article.sectionId,
                    author: article.author,
                    readingTime: article.readingTime
                });
            });
        }
        
        // فهرسة الأقسام
        if (blogData.sections) {
            blogData.sections.forEach(section => {
                this.indexItem({
                    id: section.id,
                    type: 'section',
                    title: section.name,
                    content: section.description,
                    url: `sections/${section.id}.html`,
                    keywords: section.keywords || []
                });
            });
        }
        
        // فهرسة الصفحات الإضافية
        if (blogData.additionalPages) {
            blogData.additionalPages.forEach(page => {
                this.indexItem({
                    id: this.generateSlug(page.title),
                    type: 'page',
                    title: page.title,
                    content: this.stripHTML(page.content),
                    url: `pages/${this.generateSlug(page.title)}.html`
                });
            });
        }
        
        this.isIndexed = true;
        this.generateSearchSuggestions();
    }
    
    /**
     * فهرسة عنصر واحد
     */
    indexItem(item) {
        const searchableText = [
            item.title,
            item.content,
            item.excerpt || '',
            ...(item.keywords || [])
        ].join(' ').toLowerCase();
        
        // تقسيم النص إلى كلمات
        const words = this.tokenize(searchableText);
        
        words.forEach(word => {
            if (word.length >= 2) {
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, []);
                }
                
                const existingItem = this.searchIndex.get(word).find(i => i.id === item.id);
                if (!existingItem) {
                    this.searchIndex.get(word).push({
                        ...item,
                        relevance: this.calculateRelevance(word, item)
                    });
                }
            }
        });
    }
    
    /**
     * تقسيم النص إلى كلمات
     */
    tokenize(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z0-9\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 0);
    }
    
    /**
     * حساب مدى الصلة
     */
    calculateRelevance(word, item) {
        let relevance = 0;
        
        // وزن العنوان أعلى
        if (item.title.toLowerCase().includes(word)) {
            relevance += 10;
        }
        
        // وزن الكلمات المفتاحية
        if (item.keywords && item.keywords.some(k => k.toLowerCase().includes(word))) {
            relevance += 8;
        }
        
        // وزن المحتوى
        const contentMatches = (item.content.toLowerCase().match(new RegExp(word, 'g')) || []).length;
        relevance += contentMatches * 2;
        
        // وزن نوع المحتوى
        switch (item.type) {
            case 'article':
                relevance += 5;
                break;
            case 'section':
                relevance += 3;
                break;
            case 'page':
                relevance += 1;
                break;
        }
        
        return relevance;
    }
    
    /**
     * البحث في المحتوى
     */
    search(query, options = {}) {
        if (!this.isIndexed) {
            console.warn('المحتوى غير مفهرس بعد');
            return [];
        }
        
        const searchOptions = { ...this.searchOptions, ...options };
        
        if (query.length < searchOptions.minQueryLength) {
            return [];
        }
        
        // إضافة إلى تاريخ البحث
        this.addToSearchHistory(query);
        
        const queryWords = this.tokenize(query.toLowerCase());
        const results = new Map();
        
        queryWords.forEach(word => {
            // البحث المباشر
            if (this.searchIndex.has(word)) {
                this.searchIndex.get(word).forEach(item => {
                    if (results.has(item.id)) {
                        results.get(item.id).relevance += item.relevance;
                    } else {
                        results.set(item.id, { ...item });
                    }
                });
            }
            
            // البحث الضبابي
            if (searchOptions.fuzzySearch) {
                this.searchIndex.forEach((items, indexWord) => {
                    if (this.isFuzzyMatch(word, indexWord)) {
                        items.forEach(item => {
                            if (results.has(item.id)) {
                                results.get(item.id).relevance += item.relevance * 0.7; // وزن أقل للبحث الضبابي
                            } else {
                                results.set(item.id, { ...item, relevance: item.relevance * 0.7 });
                            }
                        });
                    }
                });
            }
        });
        
        // ترتيب النتائج حسب الصلة
        const sortedResults = Array.from(results.values())
            .sort((a, b) => b.relevance - a.relevance)
            .slice(0, searchOptions.maxResults);
        
        // تمييز النتائج
        if (searchOptions.highlightResults) {
            sortedResults.forEach(result => {
                result.highlightedTitle = this.highlightText(result.title, queryWords);
                result.highlightedContent = this.highlightText(
                    result.content.substring(0, 200) + '...',
                    queryWords
                );
            });
        }
        
        return sortedResults;
    }
    
    /**
     * البحث الضبابي
     */
    isFuzzyMatch(word1, word2, threshold = 0.8) {
        if (word1.length < 3 || word2.length < 3) {
            return false;
        }
        
        const similarity = this.calculateSimilarity(word1, word2);
        return similarity >= threshold;
    }
    
    /**
     * حساب التشابه بين كلمتين
     */
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) {
            return 1.0;
        }
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }
    
    /**
     * حساب مسافة Levenshtein
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    /**
     * تمييز النص
     */
    highlightText(text, queryWords) {
        let highlightedText = text;
        
        queryWords.forEach(word => {
            const regex = new RegExp(`(${word})`, 'gi');
            highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
        });
        
        return highlightedText;
    }
    
    /**
     * إضافة إلى تاريخ البحث
     */
    addToSearchHistory(query) {
        const trimmedQuery = query.trim();
        if (trimmedQuery && !this.searchHistory.includes(trimmedQuery)) {
            this.searchHistory.unshift(trimmedQuery);
            if (this.searchHistory.length > 50) {
                this.searchHistory.pop();
            }
            this.saveSearchHistory();
        }
    }
    
    /**
     * الحصول على تاريخ البحث
     */
    getSearchHistory() {
        return this.searchHistory;
    }
    
    /**
     * مسح تاريخ البحث
     */
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }
    
    /**
     * حفظ تاريخ البحث محلياً
     */
    saveSearchHistory() {
        localStorage.setItem('blogSearchHistory', JSON.stringify(this.searchHistory));
    }
    
    /**
     * تحميل تاريخ البحث
     */
    loadSearchHistory() {
        const saved = localStorage.getItem('blogSearchHistory');
        if (saved) {
            try {
                this.searchHistory = JSON.parse(saved);
            } catch (error) {
                console.warn('فشل في تحميل تاريخ البحث:', error);
            }
        }
    }
    
    /**
     * توليد اقتراحات البحث
     */
    generateSearchSuggestions() {
        this.searchSuggestions = [];
        
        // جمع الكلمات الأكثر شيوعاً
        const wordFrequency = new Map();
        
        this.searchIndex.forEach((items, word) => {
            if (word.length >= 3) {
                const totalRelevance = items.reduce((sum, item) => sum + item.relevance, 0);
                wordFrequency.set(word, totalRelevance);
            }
        });
        
        // ترتيب الكلمات حسب الشيوع
        this.searchSuggestions = Array.from(wordFrequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20)
            .map(([word]) => word);
    }
    
    /**
     * الحصول على اقتراحات البحث
     */
    getSuggestions(query, limit = 5) {
        if (!query || query.length < 2) {
            return this.searchSuggestions.slice(0, limit);
        }
        
        const queryLower = query.toLowerCase();
        const suggestions = [];
        
        // البحث في الاقتراحات
        this.searchSuggestions.forEach(suggestion => {
            if (suggestion.includes(queryLower)) {
                suggestions.push(suggestion);
            }
        });
        
        // البحث في تاريخ البحث
        this.searchHistory.forEach(historyItem => {
            if (historyItem.toLowerCase().includes(queryLower) && !suggestions.includes(historyItem)) {
                suggestions.push(historyItem);
            }
        });
        
        return suggestions.slice(0, limit);
    }
    
    /**
     * إزالة HTML tags
     */
    stripHTML(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }
    
    /**
     * إنشاء slug
     */
    generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    
    /**
     * إنشاء واجهة البحث
     */
    createSearchInterface(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        container.innerHTML = `
            <div class="search-container">
                <div class="search-input-group">
                    <input type="text" id="searchInput" class="form-control search-input" 
                           placeholder="ابحث في المدونة..." autocomplete="off">
                    <button class="btn btn-primary search-btn" onclick="performSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="searchSuggestions" class="search-suggestions"></div>
                <div id="searchResults" class="search-results"></div>
            </div>
        `;
        
        this.setupSearchEvents();
    }
    
    /**
     * إعداد أحداث البحث
     */
    setupSearchEvents() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;
        
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.showSuggestions(e.target.value);
            }, 300);
        });
        
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.performSearch(e.target.value);
            }
        });
        
        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-container')) {
                this.hideSuggestions();
            }
        });
    }
    
    /**
     * عرض الاقتراحات
     */
    showSuggestions(query) {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (!suggestionsContainer) return;
        
        const suggestions = this.getSuggestions(query);
        
        if (suggestions.length === 0) {
            suggestionsContainer.style.display = 'none';
            return;
        }
        
        suggestionsContainer.innerHTML = suggestions
            .map(suggestion => `
                <div class="suggestion-item" onclick="selectSuggestion('${suggestion}')">
                    <i class="fas fa-search me-2"></i>
                    ${suggestion}
                </div>
            `).join('');
        
        suggestionsContainer.style.display = 'block';
    }
    
    /**
     * إخفاء الاقتراحات
     */
    hideSuggestions() {
        const suggestionsContainer = document.getElementById('searchSuggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }
    
    /**
     * تنفيذ البحث
     */
    performSearch(query) {
        if (!query) {
            const searchInput = document.getElementById('searchInput');
            query = searchInput ? searchInput.value : '';
        }
        
        const results = this.search(query);
        this.displayResults(results, query);
        this.hideSuggestions();
    }
    
    /**
     * عرض النتائج
     */
    displayResults(results, query) {
        const resultsContainer = document.getElementById('searchResults');
        if (!resultsContainer) return;
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>لا توجد نتائج</h5>
                    <p>لم نجد أي نتائج لـ "${query}"</p>
                </div>
            `;
            return;
        }
        
        resultsContainer.innerHTML = `
            <div class="results-header">
                <h5>نتائج البحث عن "${query}" (${results.length})</h5>
            </div>
            <div class="results-list">
                ${results.map(result => `
                    <div class="result-item">
                        <div class="result-type">
                            <i class="fas fa-${this.getTypeIcon(result.type)}"></i>
                            ${this.getTypeLabel(result.type)}
                        </div>
                        <h6 class="result-title">
                            <a href="${result.url}">${result.highlightedTitle || result.title}</a>
                        </h6>
                        <p class="result-content">${result.highlightedContent || result.content}</p>
                        <div class="result-meta">
                            ${result.readingTime ? `<span><i class="fas fa-clock"></i> ${result.readingTime}</span>` : ''}
                            ${result.author ? `<span><i class="fas fa-user"></i> ${result.author}</span>` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    /**
     * الحصول على أيقونة النوع
     */
    getTypeIcon(type) {
        const icons = {
            article: 'file-alt',
            section: 'folder',
            page: 'file'
        };
        return icons[type] || 'file';
    }
    
    /**
     * الحصول على تسمية النوع
     */
    getTypeLabel(type) {
        const labels = {
            article: 'مقال',
            section: 'قسم',
            page: 'صفحة'
        };
        return labels[type] || 'محتوى';
    }
}

// دوال عامة للبحث
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = suggestion;
        window.searchEngine.performSearch(suggestion);
    }
}

function performSearch(query) {
    if (window.searchEngine) {
        window.searchEngine.performSearch(query);
    }
}

// إنشاء مثيل عام من محرك البحث
window.searchEngine = new SearchEngine();
