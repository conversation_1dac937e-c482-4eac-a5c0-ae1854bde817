<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - مولد المدونات الذكي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            direction: rtl;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            margin: 2rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #f59e0b;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1e40af);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .btn-secondary:hover {
            background: #e5e7eb;
        }
        
        .offline-features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }
        
        .feature-list {
            list-style: none;
            text-align: right;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            color: #6b7280;
        }
        
        .feature-list li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-left: 0.5rem;
        }
        
        .connection-status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .status-offline {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .status-online {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        
        <p class="offline-message">
            يبدو أنك غير متصل بالإنترنت حالياً. لا تقلق، يمكنك الاستمرار في استخدام بعض ميزات التطبيق!
        </p>
        
        <div class="connection-status" id="connectionStatus">
            <span id="statusText">🔴 غير متصل</span>
        </div>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="checkConnection()">
                🔄 فحص الاتصال
            </button>
            
            <a href="/" class="btn btn-secondary">
                🏠 العودة للرئيسية
            </a>
            
            <button class="btn btn-secondary" onclick="goBack()">
                ⬅️ الصفحة السابقة
            </button>
        </div>
        
        <div class="offline-features">
            <h3 style="color: #374151; margin-bottom: 1rem;">الميزات المتاحة دون اتصال:</h3>
            <ul class="feature-list">
                <li>عرض المدونات المحفوظة مسبقاً</li>
                <li>تصفح الإعدادات والتخصيصات</li>
                <li>مراجعة التوثيق والأمثلة</li>
                <li>استخدام أدوات التصدير المحلية</li>
                <li>البحث في المحتوى المخزن</li>
            </ul>
        </div>
    </div>

    <script>
        // فحص حالة الاتصال
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status status-online';
                statusText.textContent = '🟢 متصل';
                
                // إعادة توجيه تلقائي بعد 3 ثوانٍ
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            } else {
                statusElement.className = 'connection-status status-offline';
                statusText.textContent = '🔴 غير متصل';
            }
        }
        
        // فحص الاتصال يدوياً
        function checkConnection() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.textContent = '🔄 جاري الفحص...';
            button.disabled = true;
            
            // محاولة طلب بسيط للتحقق من الاتصال
            fetch('/', { method: 'HEAD', cache: 'no-cache' })
                .then(() => {
                    updateConnectionStatus();
                    button.textContent = '✅ متصل!';
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                })
                .catch(() => {
                    updateConnectionStatus();
                    button.textContent = originalText;
                    button.disabled = false;
                });
        }
        
        // العودة للصفحة السابقة
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
        
        // مراقبة تغييرات الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // فحص أولي
        updateConnectionStatus();
        
        // فحص دوري كل 30 ثانية
        setInterval(() => {
            if (!navigator.onLine) {
                fetch('/', { method: 'HEAD', cache: 'no-cache' })
                    .then(() => {
                        // إذا نجح الطلب، فالاتصال متاح
                        if (!navigator.onLine) {
                            // تحديث حالة المتصفح
                            window.dispatchEvent(new Event('online'));
                        }
                    })
                    .catch(() => {
                        // الاتصال غير متاح
                    });
            }
        }, 30000);
        
        // رسالة ترحيب
        console.log('🌐 صفحة العمل دون اتصال محملة');
        console.log('📱 يمكنك الاستمرار في استخدام الميزات المحلية');
    </script>
</body>
</html>
