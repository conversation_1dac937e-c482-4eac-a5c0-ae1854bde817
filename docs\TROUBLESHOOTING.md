# دليل استكشاف الأخطاء وإصلاحها

## المشاكل الشائعة والحلول

### 🔌 مشاكل API

#### المشكلة: فشل في الاتصال بـ Gemini API

**الأعراض:**
- رسالة خطأ "فشل في الاتصال بـ API"
- عدم توليد أي محتوى
- تجمد التطبيق أثناء التوليد

**الحلول:**

1. **تحقق من مفاتيح API:**
```javascript
// افتح وحدة تحكم المتصفح واكتب:
console.log(window.geminiAPI.apiKeys);
// تأكد من وجود مفاتيح صالحة
```

2. **اختبار المفاتيح:**
```javascript
// اختبار جميع المفاتيح
const results = await window.geminiAPI.testAllKeys();
console.log(results);
```

3. **فحص الاتصال بالإنترنت:**
- تأكد من وجود اتصال مستقر بالإنترنت
- جرب فتح موقع Google للتأكد

4. **مسح ذاكرة التخزين المؤقت:**
```javascript
// مسح البيانات المحفوظة
localStorage.clear();
sessionStorage.clear();
location.reload();
```

#### المشكلة: تجاوز حد الاستخدام (Quota Exceeded)

**الحلول:**

1. **انتظار إعادة تعيين الحد:**
- حدود Gemini API تتجدد كل 24 ساعة
- انتظر حتى اليوم التالي

2. **استخدام مفاتيح إضافية:**
- أضف مفاتيح API جديدة إلى المصفوفة
- النظام سيقوم بالتدوير التلقائي

3. **تقليل حجم الطلبات:**
- قلل عدد المقالات لكل قسم
- استخدم كلمات مفتاحية أقل

### 📝 مشاكل توليد المحتوى

#### المشكلة: محتوى غير مكتمل أو منقطع

**الحلول:**

1. **زيادة timeout:**
```javascript
// في ملف gemini-api.js
this.retryDelay = 2000; // زيادة التأخير
this.maxRetries = 5; // زيادة المحاولات
```

2. **تقسيم المحتوى:**
- استخدم عدد كلمات أقل لكل فقرة
- قلل عدد العناوين لكل مقال

3. **فحص جودة الإنترنت:**
- تأكد من استقرار الاتصال
- تجنب استخدام شبكات بطيئة

#### المشكلة: محتوى بلغة خاطئة

**الحلول:**

1. **تأكد من إعداد اللغة:**
```javascript
// تحقق من إعداد اللغة
console.log(document.documentElement.lang);
console.log(document.documentElement.dir);
```

2. **إعادة تعيين اللغة:**
```javascript
// إعادة تعيين اللغة للعربية
settingsManager.updateSetting('language', 'ar');
```

### 💾 مشاكل التصدير

#### المشكلة: فشل في تصدير PDF

**الحلول:**

1. **تحقق من تحميل المكتبة:**
```javascript
// فحص jsPDF
if (typeof window.jspdf !== 'undefined') {
    console.log('jsPDF محمل بنجاح');
} else {
    console.error('jsPDF غير محمل');
}
```

2. **إعادة تحميل الصفحة:**
- أعد تحميل الصفحة وحاول مرة أخرى
- تأكد من اتصال الإنترنت

3. **استخدام متصفح مختلف:**
- جرب Chrome أو Firefox
- تأكد من تحديث المتصفح

#### المشكلة: ملف Excel فارغ أو تالف

**الحلول:**

1. **فحص مكتبة XLSX:**
```javascript
if (typeof XLSX !== 'undefined') {
    console.log('XLSX محمل بنجاح');
} else {
    console.error('XLSX غير محمل');
}
```

2. **تحقق من البيانات:**
```javascript
// فحص بيانات المدونة
console.log(window.exportManager.currentBlog);
```

### 🔍 مشاكل البحث

#### المشكلة: البحث لا يعطي نتائج

**الحلول:**

1. **فحص الفهرسة:**
```javascript
// تحقق من الفهرسة
console.log(window.searchEngine.isIndexed);
console.log(window.searchEngine.searchIndex.size);
```

2. **إعادة الفهرسة:**
```javascript
// إعادة فهرسة المحتوى
if (window.app.generatedBlog) {
    window.searchEngine.indexContent(window.app.generatedBlog);
}
```

3. **تجربة كلمات مختلفة:**
- استخدم كلمات أبسط
- جرب البحث بكلمة واحدة

### 🎨 مشاكل الواجهة

#### المشكلة: التخطيط مكسور أو غير صحيح

**الحلول:**

1. **مسح ذاكرة التخزين:**
```javascript
// مسح CSS المخزن مؤقتاً
location.reload(true);
```

2. **فحص وضع RTL:**
```javascript
// تأكد من وضع RTL
document.documentElement.dir = 'rtl';
document.documentElement.lang = 'ar';
```

3. **إعادة تعيين الإعدادات:**
```javascript
// إعادة تعيين إعدادات المظهر
settingsManager.resetSettings();
```

#### المشكلة: الخطوط لا تظهر بشكل صحيح

**الحلول:**

1. **فحص تحميل الخطوط:**
```css
/* في وحدة تحكم المتصفح */
document.fonts.ready.then(() => {
    console.log('جميع الخطوط محملة');
});
```

2. **استخدام خطوط احتياطية:**
```css
font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
```

### 📱 مشاكل الأجهزة المحمولة

#### المشكلة: الواجهة لا تعمل على الهاتف

**الحلول:**

1. **فحص viewport:**
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0">
```

2. **تعطيل الزوم:**
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
```

3. **استخدام متصفح مختلف:**
- جرب Chrome Mobile
- تجنب المتصفحات القديمة

### 🔧 أدوات التشخيص

#### فحص حالة النظام

```javascript
// دالة فحص شاملة
function systemDiagnostics() {
    const report = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        language: navigator.language,
        online: navigator.onLine,
        cookiesEnabled: navigator.cookieEnabled,
        localStorage: typeof Storage !== 'undefined',
        
        // فحص المكتبات
        libraries: {
            bootstrap: typeof bootstrap !== 'undefined',
            jspdf: typeof window.jspdf !== 'undefined',
            xlsx: typeof XLSX !== 'undefined',
            jszip: typeof JSZip !== 'undefined'
        },
        
        // فحص المكونات
        components: {
            geminiAPI: typeof window.geminiAPI !== 'undefined',
            blogGenerator: typeof window.blogGenerator !== 'undefined',
            exportManager: typeof window.exportManager !== 'undefined',
            searchEngine: typeof window.searchEngine !== 'undefined',
            notificationManager: typeof window.notificationManager !== 'undefined',
            settingsManager: typeof window.settingsManager !== 'undefined'
        },
        
        // فحص البيانات
        data: {
            hasGeneratedBlog: window.app?.generatedBlog !== null,
            searchIndexed: window.searchEngine?.isIndexed || false,
            settingsLoaded: Object.keys(window.settingsManager?.settings || {}).length > 0
        }
    };
    
    console.table(report.libraries);
    console.table(report.components);
    console.table(report.data);
    
    return report;
}

// تشغيل التشخيص
systemDiagnostics();
```

#### فحص الأداء

```javascript
// مراقبة الأداء
function performanceCheck() {
    const perf = performance.getEntriesByType('navigation')[0];
    
    console.log('أداء التحميل:');
    console.log(`- وقت التحميل الكامل: ${perf.loadEventEnd - perf.navigationStart}ms`);
    console.log(`- وقت تحليل DOM: ${perf.domContentLoadedEventEnd - perf.navigationStart}ms`);
    console.log(`- وقت الاستجابة: ${perf.responseEnd - perf.requestStart}ms`);
    
    // فحص الذاكرة (Chrome فقط)
    if (performance.memory) {
        console.log('استخدام الذاكرة:');
        console.log(`- المستخدمة: ${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`- المخصصة: ${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`- الحد الأقصى: ${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
    }
}

performanceCheck();
```

### 📞 طلب المساعدة

إذا لم تحل المشاكل أعلاه مشكلتك:

1. **جمع معلومات التشخيص:**
```javascript
// تشغيل هذا الكود وحفظ النتيجة
const diagnostics = systemDiagnostics();
console.log('نسخ هذه المعلومات عند طلب المساعدة:', JSON.stringify(diagnostics, null, 2));
```

2. **فحص وحدة تحكم المتصفح:**
- اضغط F12 لفتح أدوات المطور
- انتقل إلى تبويب Console
- ابحث عن رسائل الخطأ باللون الأحمر

3. **تجربة صفحة الاختبار:**
- افتح `test-blog.html`
- شغل الاختبار الشامل
- راجع النتائج في السجل

4. **التواصل مع الدعم:**
- [GitHub Issues](https://github.com/blog-generator/issues)
- [Discord Server](https://discord.gg/blog-generator)
- البريد الإلكتروني: <EMAIL>

### 🔄 إعادة التعيين الكاملة

كحل أخير، يمكنك إعادة تعيين التطبيق بالكامل:

```javascript
// إعادة تعيين شاملة
function fullReset() {
    // مسح جميع البيانات المحفوظة
    localStorage.clear();
    sessionStorage.clear();
    
    // إعادة تعيين الإعدادات
    if (window.settingsManager) {
        window.settingsManager.resetSettings();
    }
    
    // مسح ذاكرة البحث
    if (window.searchEngine) {
        window.searchEngine.clearStoredData();
    }
    
    // إعادة تحميل الصفحة
    location.reload(true);
}

// تشغيل إعادة التعيين (احذر: سيمسح جميع البيانات!)
// fullReset();
```

---

**ملاحظة:** احتفظ بنسخة احتياطية من بياناتك المهمة قبل تطبيق الحلول المتقدمة.
