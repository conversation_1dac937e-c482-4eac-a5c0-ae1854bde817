/**
 * Export Manager - مدير التصدير
 * يدير تصدير المدونات بصيغ متعددة
 */

class ExportManager {
    constructor() {
        this.currentBlog = null;
        this.exportFormats = ['html', 'pdf', 'excel', 'text', 'zip'];
    }
    
    /**
     * تعيين المدونة الحالية للتصدير
     */
    setBlog(blog) {
        this.currentBlog = blog;
    }
    
    /**
     * تصدير المدونة بصيغة HTML
     */
    async exportAsHTML() {
        if (!this.currentBlog) {
            throw new Error('لا توجد مدونة للتصدير');
        }
        
        const htmlContent = this.generateCompleteHTML();
        const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
        this.downloadFile(blob, `${this.currentBlog.metadata.blogName}-blog.html`);
        
        return htmlContent;
    }
    
    /**
     * تصدير المدونة بصيغة PDF
     */
    async exportAsPDF() {
        if (!this.currentBlog) {
            throw new Error('لا توجد مدونة للتصدير');
        }
        
        // إنشاء محتوى PDF باستخدام jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });
        
        // إعداد الخط العربي
        doc.setFont('Arial', 'normal');
        doc.setFontSize(16);
        
        let yPosition = 20;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 20;
        
        // عنوان المدونة
        doc.text(this.currentBlog.metadata.blogName, margin, yPosition, { align: 'right' });
        yPosition += 15;
        
        // فهرس المحتويات
        doc.setFontSize(14);
        doc.text('فهرس المحتويات', margin, yPosition, { align: 'right' });
        yPosition += 10;
        
        // إضافة الأقسام والمقالات
        this.currentBlog.sections.forEach((section, sectionIndex) => {
            if (yPosition > pageHeight - 30) {
                doc.addPage();
                yPosition = 20;
            }
            
            doc.setFontSize(12);
            doc.text(`${sectionIndex + 1}. ${section.name}`, margin + 5, yPosition, { align: 'right' });
            yPosition += 8;
            
            const sectionArticles = this.currentBlog.articles.filter(a => a.sectionId === section.id);
            sectionArticles.forEach((article, articleIndex) => {
                if (yPosition > pageHeight - 30) {
                    doc.addPage();
                    yPosition = 20;
                }
                
                doc.setFontSize(10);
                doc.text(`   ${sectionIndex + 1}.${articleIndex + 1} ${article.title}`, margin + 10, yPosition, { align: 'right' });
                yPosition += 6;
            });
            
            yPosition += 5;
        });
        
        // إضافة محتوى المقالات
        doc.addPage();
        yPosition = 20;
        
        this.currentBlog.articles.forEach((article, index) => {
            if (yPosition > pageHeight - 50) {
                doc.addPage();
                yPosition = 20;
            }
            
            // عنوان المقال
            doc.setFontSize(14);
            doc.text(article.title, margin, yPosition, { align: 'right' });
            yPosition += 15;
            
            // محتوى المقال (مبسط)
            doc.setFontSize(10);
            const content = this.stripHTML(article.content);
            const lines = doc.splitTextToSize(content, doc.internal.pageSize.width - 2 * margin);
            
            lines.forEach(line => {
                if (yPosition > pageHeight - 20) {
                    doc.addPage();
                    yPosition = 20;
                }
                doc.text(line, margin, yPosition, { align: 'right' });
                yPosition += 5;
            });
            
            yPosition += 10;
        });
        
        // حفظ الملف
        doc.save(`${this.currentBlog.metadata.blogName}-blog.pdf`);
        
        return doc.output('blob');
    }
    
    /**
     * تصدير المدونة بصيغة Excel
     */
    async exportAsExcel() {
        if (!this.currentBlog) {
            throw new Error('لا توجد مدونة للتصدير');
        }
        
        const workbook = XLSX.utils.book_new();
        
        // ورقة المعلومات العامة
        const blogInfo = [
            ['اسم المدونة', this.currentBlog.metadata.blogName],
            ['اللغة', this.currentBlog.metadata.language],
            ['تاريخ الإنشاء', new Date(this.currentBlog.generatedAt).toLocaleDateString('ar-SA')],
            ['عدد الأقسام', this.currentBlog.sections.length],
            ['عدد المقالات', this.currentBlog.articles.length]
        ];
        
        const infoSheet = XLSX.utils.aoa_to_sheet(blogInfo);
        XLSX.utils.book_append_sheet(workbook, infoSheet, 'معلومات المدونة');
        
        // ورقة الأقسام
        const sectionsData = [
            ['اسم القسم', 'الوصف', 'عدد المقالات']
        ];
        
        this.currentBlog.sections.forEach(section => {
            const articleCount = this.currentBlog.articles.filter(a => a.sectionId === section.id).length;
            sectionsData.push([
                section.name,
                this.stripHTML(section.description),
                articleCount
            ]);
        });
        
        const sectionsSheet = XLSX.utils.aoa_to_sheet(sectionsData);
        XLSX.utils.book_append_sheet(workbook, sectionsSheet, 'الأقسام');
        
        // ورقة المقالات
        const articlesData = [
            ['العنوان', 'القسم', 'المحتوى', 'الكلمات المفتاحية', 'وقت القراءة']
        ];
        
        this.currentBlog.articles.forEach(article => {
            const section = this.currentBlog.sections.find(s => s.id === article.sectionId);
            articlesData.push([
                article.title,
                section ? section.name : 'غير محدد',
                this.stripHTML(article.content).substring(0, 500) + '...',
                article.keywords.join(', '),
                article.readingTime
            ]);
        });
        
        const articlesSheet = XLSX.utils.aoa_to_sheet(articlesData);
        XLSX.utils.book_append_sheet(workbook, articlesSheet, 'المقالات');
        
        // حفظ الملف
        XLSX.writeFile(workbook, `${this.currentBlog.metadata.blogName}-blog.xlsx`);
        
        return workbook;
    }
    
    /**
     * تصدير المدونة بصيغة نص
     */
    async exportAsText() {
        if (!this.currentBlog) {
            throw new Error('لا توجد مدونة للتصدير');
        }
        
        let textContent = '';
        
        // معلومات المدونة
        textContent += `مدونة: ${this.currentBlog.metadata.blogName}\n`;
        textContent += `اللغة: ${this.currentBlog.metadata.language}\n`;
        textContent += `تاريخ الإنشاء: ${new Date(this.currentBlog.generatedAt).toLocaleDateString('ar-SA')}\n`;
        textContent += `عدد الأقسام: ${this.currentBlog.sections.length}\n`;
        textContent += `عدد المقالات: ${this.currentBlog.articles.length}\n`;
        textContent += '\n' + '='.repeat(50) + '\n\n';
        
        // فهرس المحتويات
        textContent += 'فهرس المحتويات:\n\n';
        this.currentBlog.sections.forEach((section, sectionIndex) => {
            textContent += `${sectionIndex + 1}. ${section.name}\n`;
            const sectionArticles = this.currentBlog.articles.filter(a => a.sectionId === section.id);
            sectionArticles.forEach((article, articleIndex) => {
                textContent += `   ${sectionIndex + 1}.${articleIndex + 1} ${article.title}\n`;
            });
            textContent += '\n';
        });
        
        textContent += '\n' + '='.repeat(50) + '\n\n';
        
        // محتوى الأقسام والمقالات
        this.currentBlog.sections.forEach((section, sectionIndex) => {
            textContent += `القسم ${sectionIndex + 1}: ${section.name}\n`;
            textContent += '-'.repeat(30) + '\n';
            textContent += `${this.stripHTML(section.description)}\n\n`;
            
            const sectionArticles = this.currentBlog.articles.filter(a => a.sectionId === section.id);
            sectionArticles.forEach((article, articleIndex) => {
                textContent += `المقال ${sectionIndex + 1}.${articleIndex + 1}: ${article.title}\n`;
                textContent += `وقت القراءة: ${article.readingTime}\n`;
                textContent += `الكلمات المفتاحية: ${article.keywords.join(', ')}\n\n`;
                textContent += `${this.stripHTML(article.content)}\n\n`;
                textContent += '-'.repeat(50) + '\n\n';
            });
        });
        
        const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });
        this.downloadFile(blob, `${this.currentBlog.metadata.blogName}-blog.txt`);
        
        return textContent;
    }
    
    /**
     * تصدير المدونة كملف مضغوط
     */
    async exportAsZip() {
        if (!this.currentBlog) {
            throw new Error('لا توجد مدونة للتصدير');
        }
        
        const zip = new JSZip();
        
        // إضافة ملف HTML الرئيسي
        const htmlContent = this.generateCompleteHTML();
        zip.file('index.html', htmlContent);
        
        // إضافة ملفات CSS
        const cssContent = this.generateCSS();
        zip.file('assets/css/style.css', cssContent);
        
        // إضافة ملفات JavaScript
        const jsContent = this.generateJS();
        zip.file('assets/js/script.js', jsContent);
        
        // إضافة صفحات المقالات
        const articlesFolder = zip.folder('articles');
        this.currentBlog.articles.forEach(article => {
            const articleHTML = this.generateArticleHTML(article);
            articlesFolder.file(`${article.id}.html`, articleHTML);
        });
        
        // إضافة صفحات الأقسام
        const sectionsFolder = zip.folder('sections');
        this.currentBlog.sections.forEach(section => {
            const sectionHTML = this.generateSectionHTML(section);
            sectionsFolder.file(`${section.id}.html`, sectionHTML);
        });
        
        // إضافة الصفحات الإضافية
        if (this.currentBlog.additionalPages) {
            const pagesFolder = zip.folder('pages');
            this.currentBlog.additionalPages.forEach(page => {
                const pageHTML = this.generatePageHTML(page);
                pagesFolder.file(`${this.generateSlug(page.title)}.html`, pageHTML);
            });
        }
        
        // إضافة ملف README
        const readmeContent = this.generateReadme();
        zip.file('README.md', readmeContent);
        
        // إنشاء وتحميل الملف المضغوط
        const content = await zip.generateAsync({ type: 'blob' });
        this.downloadFile(content, `${this.currentBlog.metadata.blogName}-blog.zip`);
        
        return content;
    }
    
    /**
     * إنشاء HTML كامل للمدونة
     */
    generateCompleteHTML() {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.currentBlog.metadata.blogName}</title>
    <meta name="description" content="${this.currentBlog.seo?.metaDescription || 'مدونة احترافية'}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>${this.generateCSS()}</style>
</head>
<body>
    ${this.generateHeader()}
    ${this.generateHero()}
    ${this.generateSections()}
    ${this.generateArticles()}
    ${this.generateFooter()}
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>${this.generateJS()}</script>
</body>
</html>`;
    }
    
    /**
     * إنشاء CSS للمدونة
     */
    generateCSS() {
        return `
        body { font-family: 'Cairo', 'Tajawal', sans-serif; }
        .hero-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 100px 0; }
        .section-card { background: white; border-radius: 15px; padding: 30px; margin: 20px 0; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .article-card { border: none; border-radius: 15px; overflow: hidden; transition: transform 0.3s; }
        .article-card:hover { transform: translateY(-5px); }
        .search-box { background: white; border-radius: 25px; padding: 15px 25px; margin: 20px 0; }
        `;
    }
    
    /**
     * إنشاء JavaScript للمدونة
     */
    generateJS() {
        return `
        // محرك البحث
        function searchBlog() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const articles = document.querySelectorAll('.article-card');
            
            articles.forEach(article => {
                const title = article.querySelector('.card-title').textContent.toLowerCase();
                const content = article.querySelector('.card-text').textContent.toLowerCase();
                
                if (title.includes(query) || content.includes(query)) {
                    article.style.display = 'block';
                } else {
                    article.style.display = 'none';
                }
            });
        }
        
        // تفعيل البحث عند الكتابة
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', searchBlog);
            }
        });
        `;
    }
    
    /**
     * إنشاء رأس الصفحة
     */
    generateHeader() {
        return `
        <header class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="#">${this.currentBlog.metadata.blogName}</a>
                <div class="navbar-nav">
                    ${this.currentBlog.sections.map(section => 
                        `<a class="nav-link" href="#${section.id}">${section.name}</a>`
                    ).join('')}
                </div>
            </div>
        </header>
        `;
    }
    
    /**
     * إنشاء قسم Hero
     */
    generateHero() {
        const heroContent = this.currentBlog.content?.heroTitle || this.currentBlog.metadata.blogName;
        const heroDescription = this.currentBlog.content?.heroDescription || 'مدونة احترافية متخصصة';
        
        return `
        <section class="hero-section text-center">
            <div class="container">
                <h1 class="display-4 mb-4">${heroContent}</h1>
                <p class="lead mb-4">${heroDescription}</p>
                <div class="search-box d-inline-block">
                    <input type="text" id="searchInput" class="form-control" placeholder="ابحث في المدونة...">
                </div>
            </div>
        </section>
        `;
    }
    
    /**
     * إنشاء الأقسام
     */
    generateSections() {
        return this.currentBlog.sections.map(section => `
            <section id="${section.id}" class="py-5">
                <div class="container">
                    <div class="section-card">
                        <h2 class="text-center mb-4">${section.name}</h2>
                        <p class="text-center text-muted mb-5">${section.description}</p>
                        <div class="row">
                            ${this.generateSectionArticles(section.id)}
                        </div>
                    </div>
                </div>
            </section>
        `).join('');
    }
    
    /**
     * إنشاء مقالات القسم
     */
    generateSectionArticles(sectionId) {
        const sectionArticles = this.currentBlog.articles.filter(a => a.sectionId === sectionId);
        
        return sectionArticles.map(article => `
            <div class="col-md-4 mb-4">
                <div class="card article-card h-100">
                    <div class="card-body">
                        <h5 class="card-title">${article.title}</h5>
                        <p class="card-text">${article.excerpt || this.stripHTML(article.content).substring(0, 150) + '...'}</p>
                        <small class="text-muted">وقت القراءة: ${article.readingTime}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    /**
     * إنشاء جميع المقالات
     */
    generateArticles() {
        return `
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="text-center mb-5">جميع المقالات</h2>
                <div class="row">
                    ${this.currentBlog.articles.map(article => `
                        <div class="col-lg-6 mb-4">
                            <div class="card article-card">
                                <div class="card-body">
                                    <h5 class="card-title">${article.title}</h5>
                                    <p class="card-text">${this.stripHTML(article.content).substring(0, 200) + '...'}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">${article.readingTime}</small>
                                        <span class="badge bg-primary">${article.keywords[0] || 'عام'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </section>
        `;
    }
    
    /**
     * إنشاء تذييل الصفحة
     */
    generateFooter() {
        return `
        <footer class="bg-dark text-light py-4">
            <div class="container text-center">
                <p>&copy; 2024 ${this.currentBlog.metadata.blogName}. جميع الحقوق محفوظة.</p>
                <p>تم إنشاؤها بواسطة مولد المدونات الذكي</p>
            </div>
        </footer>
        `;
    }
    
    /**
     * إزالة HTML tags من النص
     */
    stripHTML(html) {
        const tmp = document.createElement('div');
        tmp.innerHTML = html;
        return tmp.textContent || tmp.innerText || '';
    }
    
    /**
     * إنشاء slug من النص
     */
    generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    
    /**
     * تحميل ملف
     */
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    /**
     * إنشاء HTML لمقال منفرد
     */
    generateArticleHTML(article) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${article.title} - ${this.currentBlog.metadata.blogName}</title>
    <meta name="description" content="${article.metaDescription}">
</head>
<body>
    <article>
        <h1>${article.title}</h1>
        <div class="article-meta">
            <span>وقت القراءة: ${article.readingTime}</span>
            <span>الكلمات المفتاحية: ${article.keywords.join(', ')}</span>
        </div>
        <div class="article-content">
            ${article.content}
        </div>
    </article>
</body>
</html>`;
    }
    
    /**
     * إنشاء HTML لقسم منفرد
     */
    generateSectionHTML(section) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${section.name} - ${this.currentBlog.metadata.blogName}</title>
</head>
<body>
    <section>
        <h1>${section.name}</h1>
        <p>${section.description}</p>
    </section>
</body>
</html>`;
    }
    
    /**
     * إنشاء HTML لصفحة منفردة
     */
    generatePageHTML(page) {
        return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${page.title} - ${this.currentBlog.metadata.blogName}</title>
</head>
<body>
    <div class="page-content">
        <h1>${page.title}</h1>
        ${page.content}
    </div>
</body>
</html>`;
    }
    
    /**
     * إنشاء ملف README
     */
    generateReadme() {
        return `# ${this.currentBlog.metadata.blogName}

مدونة احترافية تم إنشاؤها بواسطة مولد المدونات الذكي

## معلومات المدونة
- **الاسم**: ${this.currentBlog.metadata.blogName}
- **اللغة**: ${this.currentBlog.metadata.language}
- **عدد الأقسام**: ${this.currentBlog.sections.length}
- **عدد المقالات**: ${this.currentBlog.articles.length}
- **تاريخ الإنشاء**: ${new Date(this.currentBlog.generatedAt).toLocaleDateString('ar-SA')}

## هيكل الملفات
- \`index.html\` - الصفحة الرئيسية
- \`articles/\` - مجلد المقالات
- \`sections/\` - مجلد الأقسام
- \`pages/\` - الصفحات الإضافية
- \`assets/\` - الموارد (CSS, JS)

## الأقسام
${this.currentBlog.sections.map((section, index) => `${index + 1}. ${section.name}`).join('\n')}

## تم إنشاؤها بواسطة
مولد المدونات الذكي - أداة متقدمة لإنشاء مدونات احترافية بالذكاء الاصطناعي
`;
    }
}

// إنشاء مثيل عام من مدير التصدير
window.exportManager = new ExportManager();
