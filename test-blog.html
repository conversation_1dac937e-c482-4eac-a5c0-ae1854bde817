<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مولد المدونات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 800px;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
        }
        .test-section.success {
            border-color: #10b981;
            background: #f0fdf4;
        }
        .test-section.error {
            border-color: #ef4444;
            background: #fef2f2;
        }
        .test-section.warning {
            border-color: #f59e0b;
            background: #fffbeb;
        }
        .test-button {
            margin: 0.5rem;
        }
        .api-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .api-status.working {
            background: #dcfce7;
            color: #166534;
        }
        .api-status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        .api-status.testing {
            background: #fef3c7;
            color: #92400e;
        }
        .log-area {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-container">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="fas fa-vial me-2"></i>
                    اختبار مولد المدونات الذكي
                </h1>
                <p class="lead text-muted">اختبار شامل لجميع وظائف الأداة</p>
            </div>

            <!-- اختبار API -->
            <div class="test-section" id="apiTestSection">
                <h3><i class="fas fa-plug me-2"></i>اختبار Gemini API</h3>
                <p>اختبار الاتصال بـ Gemini API وتدوير المفاتيح</p>
                <div class="mb-3">
                    <button class="btn btn-primary test-button" onclick="testSingleAPI()">
                        <i class="fas fa-play"></i> اختبار مفتاح واحد
                    </button>
                    <button class="btn btn-info test-button" onclick="testAllAPIs()">
                        <i class="fas fa-list"></i> اختبار جميع المفاتيح
                    </button>
                    <button class="btn btn-secondary test-button" onclick="showAPIStats()">
                        <i class="fas fa-chart-bar"></i> إحصائيات API
                    </button>
                </div>
                <div id="apiResults"></div>
            </div>

            <!-- اختبار توليد المحتوى -->
            <div class="test-section" id="contentTestSection">
                <h3><i class="fas fa-magic me-2"></i>اختبار توليد المحتوى</h3>
                <p>اختبار توليد أجزاء مختلفة من المدونة</p>
                <div class="mb-3">
                    <button class="btn btn-success test-button" onclick="testBlogStructure()">
                        <i class="fas fa-sitemap"></i> اختبار هيكل المدونة
                    </button>
                    <button class="btn btn-success test-button" onclick="testArticleGeneration()">
                        <i class="fas fa-file-alt"></i> اختبار توليد مقال
                    </button>
                    <button class="btn btn-success test-button" onclick="testSEOGeneration()">
                        <i class="fas fa-search"></i> اختبار توليد السيو
                    </button>
                </div>
                <div id="contentResults"></div>
            </div>

            <!-- اختبار التصدير -->
            <div class="test-section" id="exportTestSection">
                <h3><i class="fas fa-download me-2"></i>اختبار التصدير</h3>
                <p>اختبار تصدير المدونة بصيغ مختلفة</p>
                <div class="mb-3">
                    <button class="btn btn-warning test-button" onclick="testHTMLExport()">
                        <i class="fab fa-html5"></i> اختبار HTML
                    </button>
                    <button class="btn btn-warning test-button" onclick="testPDFExport()">
                        <i class="fas fa-file-pdf"></i> اختبار PDF
                    </button>
                    <button class="btn btn-warning test-button" onclick="testExcelExport()">
                        <i class="fas fa-file-excel"></i> اختبار Excel
                    </button>
                </div>
                <div id="exportResults"></div>
            </div>

            <!-- اختبار البحث -->
            <div class="test-section" id="searchTestSection">
                <h3><i class="fas fa-search me-2"></i>اختبار محرك البحث</h3>
                <p>اختبار فهرسة المحتوى والبحث</p>
                <div class="mb-3">
                    <button class="btn btn-info test-button" onclick="testSearchIndexing()">
                        <i class="fas fa-database"></i> اختبار الفهرسة
                    </button>
                    <button class="btn btn-info test-button" onclick="testSearchFunctionality()">
                        <i class="fas fa-search-plus"></i> اختبار البحث
                    </button>
                </div>
                <div id="searchResults"></div>
            </div>

            <!-- اختبار الإشعارات -->
            <div class="test-section" id="notificationTestSection">
                <h3><i class="fas fa-bell me-2"></i>اختبار الإشعارات</h3>
                <p>اختبار نظام الإشعارات والتنبيهات</p>
                <div class="mb-3">
                    <button class="btn btn-success test-button" onclick="testSuccessNotification()">
                        <i class="fas fa-check"></i> إشعار نجاح
                    </button>
                    <button class="btn btn-danger test-button" onclick="testErrorNotification()">
                        <i class="fas fa-times"></i> إشعار خطأ
                    </button>
                    <button class="btn btn-warning test-button" onclick="testWarningNotification()">
                        <i class="fas fa-exclamation"></i> إشعار تحذير
                    </button>
                    <button class="btn btn-info test-button" onclick="testLoadingNotification()">
                        <i class="fas fa-spinner"></i> إشعار تحميل
                    </button>
                </div>
            </div>

            <!-- سجل الأحداث -->
            <div class="test-section">
                <h3><i class="fas fa-terminal me-2"></i>سجل الأحداث</h3>
                <div class="mb-3">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportLog()">
                        <i class="fas fa-download"></i> تصدير السجل
                    </button>
                </div>
                <div id="logArea" class="log-area">جاري تحميل نظام الاختبار...</div>
            </div>

            <!-- اختبار شامل -->
            <div class="text-center mt-4">
                <button class="btn btn-primary btn-lg" onclick="runFullTest()">
                    <i class="fas fa-rocket me-2"></i>
                    تشغيل اختبار شامل
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <script src="assets/js/gemini-api.js"></script>
    <script src="assets/js/blog-generator.js"></script>
    <script src="assets/js/export-manager.js"></script>
    <script src="assets/js/search-engine.js"></script>
    <script src="assets/js/icon-manager.js"></script>
    <script src="assets/js/settings-manager.js"></script>
    <script src="assets/js/notification-manager.js"></script>
    <script src="assets/js/analytics-manager.js"></script>

    <script>
        // نظام السجل
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = {
                'info': 'ℹ️',
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'test': '🧪'
            }[type] || 'ℹ️';
            
            logArea.textContent += `[${timestamp}] ${icon} ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logArea').textContent = '';
            log('تم مسح السجل', 'info');
        }

        function exportLog() {
            const logContent = document.getElementById('logArea').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-log-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // اختبارات API
        async function testSingleAPI() {
            log('بدء اختبار مفتاح API واحد...', 'test');
            try {
                const result = await window.geminiAPI.makeRequest('اكتب كلمة واحدة: مرحبا');
                log(`نجح الاختبار: ${result.substring(0, 50)}...`, 'success');
                updateAPIStatus('working');
            } catch (error) {
                log(`فشل الاختبار: ${error.message}`, 'error');
                updateAPIStatus('error');
            }
        }

        async function testAllAPIs() {
            log('بدء اختبار جميع مفاتيح API...', 'test');
            try {
                const results = await window.geminiAPI.testAllKeys();
                const working = results.filter(r => r.status === 'يعمل').length;
                log(`تم اختبار ${results.length} مفتاح، يعمل منها ${working}`, 'info');
                
                results.forEach((result, index) => {
                    log(`المفتاح ${result.keyIndex}: ${result.status}`, result.status === 'يعمل' ? 'success' : 'error');
                });
            } catch (error) {
                log(`خطأ في اختبار المفاتيح: ${error.message}`, 'error');
            }
        }

        function showAPIStats() {
            const stats = window.geminiAPI.getStats();
            log(`إحصائيات API:`, 'info');
            log(`- إجمالي الطلبات: ${stats.totalRequests}`, 'info');
            log(`- الطلبات الناجحة: ${stats.successfulRequests}`, 'info');
            log(`- الطلبات الفاشلة: ${stats.failedRequests}`, 'info');
            log(`- معدل النجاح: ${stats.successRate}`, 'info');
            log(`- المفتاح الحالي: ${stats.currentKey}/${stats.totalKeys}`, 'info');
        }

        function updateAPIStatus(status) {
            const section = document.getElementById('apiTestSection');
            section.className = `test-section ${status === 'working' ? 'success' : 'error'}`;
        }

        // اختبارات المحتوى
        async function testBlogStructure() {
            log('اختبار توليد هيكل المدونة...', 'test');
            try {
                const testData = {
                    blogName: 'مدونة تجريبية',
                    language: 'ar',
                    sections: 'تقنية، صحة، تعليم',
                    complexity: 'متوسط'
                };
                
                // محاكاة توليد الهيكل
                log('جاري توليد الهيكل...', 'info');
                setTimeout(() => {
                    log('تم توليد هيكل المدونة بنجاح', 'success');
                }, 2000);
                
            } catch (error) {
                log(`خطأ في توليد الهيكل: ${error.message}`, 'error');
            }
        }

        async function testArticleGeneration() {
            log('اختبار توليد مقال...', 'test');
            try {
                const result = await window.geminiAPI.makeRequest(
                    'اكتب مقال قصير عن الذكاء الاصطناعي في 100 كلمة'
                );
                log('تم توليد المقال بنجاح', 'success');
                log(`عينة من المقال: ${result.substring(0, 100)}...`, 'info');
            } catch (error) {
                log(`خطأ في توليد المقال: ${error.message}`, 'error');
            }
        }

        async function testSEOGeneration() {
            log('اختبار توليد بيانات السيو...', 'test');
            try {
                const result = await window.geminiAPI.makeRequest(
                    'أنشئ عنوان SEO ووصف meta لمدونة تقنية'
                );
                log('تم توليد بيانات السيو بنجاح', 'success');
                log(`عينة: ${result.substring(0, 100)}...`, 'info');
            } catch (error) {
                log(`خطأ في توليد السيو: ${error.message}`, 'error');
            }
        }

        // اختبارات التصدير
        function testHTMLExport() {
            log('اختبار تصدير HTML...', 'test');
            try {
                // محاكاة التصدير
                const htmlContent = '<!DOCTYPE html><html><head><title>Test</title></head><body><h1>مدونة تجريبية</h1></body></html>';
                const blob = new Blob([htmlContent], { type: 'text/html' });
                log('تم إنشاء ملف HTML بنجاح', 'success');
                log(`حجم الملف: ${blob.size} بايت`, 'info');
            } catch (error) {
                log(`خطأ في تصدير HTML: ${error.message}`, 'error');
            }
        }

        function testPDFExport() {
            log('اختبار تصدير PDF...', 'test');
            try {
                if (typeof window.jspdf !== 'undefined') {
                    log('مكتبة jsPDF محملة بنجاح', 'success');
                    log('يمكن تصدير PDF', 'info');
                } else {
                    log('مكتبة jsPDF غير محملة', 'error');
                }
            } catch (error) {
                log(`خطأ في اختبار PDF: ${error.message}`, 'error');
            }
        }

        function testExcelExport() {
            log('اختبار تصدير Excel...', 'test');
            try {
                if (typeof XLSX !== 'undefined') {
                    log('مكتبة XLSX محملة بنجاح', 'success');
                    log('يمكن تصدير Excel', 'info');
                } else {
                    log('مكتبة XLSX غير محملة', 'error');
                }
            } catch (error) {
                log(`خطأ في اختبار Excel: ${error.message}`, 'error');
            }
        }

        // اختبارات البحث
        function testSearchIndexing() {
            log('اختبار فهرسة المحتوى...', 'test');
            try {
                const testData = {
                    articles: [
                        { id: 'test1', title: 'مقال تجريبي', content: 'محتوى تجريبي للاختبار', keywords: ['اختبار', 'تجريبي'] }
                    ],
                    sections: [
                        { id: 'tech', name: 'تقنية', description: 'قسم التقنية' }
                    ]
                };
                
                window.searchEngine.indexContent(testData);
                log('تم فهرسة المحتوى بنجاح', 'success');
                log(`تم فهرسة ${testData.articles.length} مقال و ${testData.sections.length} قسم`, 'info');
            } catch (error) {
                log(`خطأ في الفهرسة: ${error.message}`, 'error');
            }
        }

        function testSearchFunctionality() {
            log('اختبار وظيفة البحث...', 'test');
            try {
                const results = window.searchEngine.search('تجريبي');
                log(`تم العثور على ${results.length} نتيجة`, 'success');
                results.forEach(result => {
                    log(`- ${result.title} (نوع: ${result.type})`, 'info');
                });
            } catch (error) {
                log(`خطأ في البحث: ${error.message}`, 'error');
            }
        }

        // اختبارات الإشعارات
        function testSuccessNotification() {
            window.notificationManager.success('هذا إشعار نجاح تجريبي!', 'نجح الاختبار');
            log('تم عرض إشعار النجاح', 'success');
        }

        function testErrorNotification() {
            window.notificationManager.error('هذا إشعار خطأ تجريبي!', 'خطأ في الاختبار');
            log('تم عرض إشعار الخطأ', 'success');
        }

        function testWarningNotification() {
            window.notificationManager.warning('هذا إشعار تحذير تجريبي!', 'تحذير');
            log('تم عرض إشعار التحذير', 'success');
        }

        function testLoadingNotification() {
            const id = window.notificationManager.loading('جاري تحميل البيانات...', 'تحميل');
            log('تم عرض إشعار التحميل', 'success');
            
            setTimeout(() => {
                window.notificationManager.hide(id);
                window.notificationManager.success('تم التحميل بنجاح!');
                log('تم إخفاء إشعار التحميل', 'info');
            }, 3000);
        }

        // اختبار شامل
        async function runFullTest() {
            log('🚀 بدء الاختبار الشامل...', 'test');
            log('='.repeat(50), 'info');
            
            // اختبار API
            log('1. اختبار API...', 'test');
            await testSingleAPI();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار المحتوى
            log('2. اختبار توليد المحتوى...', 'test');
            await testArticleGeneration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار البحث
            log('3. اختبار البحث...', 'test');
            testSearchIndexing();
            testSearchFunctionality();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // اختبار التصدير
            log('4. اختبار التصدير...', 'test');
            testHTMLExport();
            testPDFExport();
            testExcelExport();
            
            // اختبار الإشعارات
            log('5. اختبار الإشعارات...', 'test');
            testSuccessNotification();
            
            log('='.repeat(50), 'info');
            log('✅ تم إكمال الاختبار الشامل!', 'success');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل صفحة الاختبار', 'success');
            log('جميع الأنظمة جاهزة للاختبار', 'info');
            
            // اختبار تحميل المكتبات
            if (typeof window.geminiAPI !== 'undefined') {
                log('✅ Gemini API محمل', 'success');
            } else {
                log('❌ Gemini API غير محمل', 'error');
            }
            
            if (typeof window.blogGenerator !== 'undefined') {
                log('✅ Blog Generator محمل', 'success');
            } else {
                log('❌ Blog Generator غير محمل', 'error');
            }
            
            if (typeof window.searchEngine !== 'undefined') {
                log('✅ Search Engine محمل', 'success');
            } else {
                log('❌ Search Engine غير محمل', 'error');
            }
            
            if (typeof window.notificationManager !== 'undefined') {
                log('✅ Notification Manager محمل', 'success');
            } else {
                log('❌ Notification Manager غير محمل', 'error');
            }
        });
    </script>
</body>
</html>
