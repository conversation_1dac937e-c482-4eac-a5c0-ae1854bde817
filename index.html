<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد المدونات الذكي - أداة احترافية لإنشاء مدونات كاملة بالذكاء الاصطناعي</title>
    <meta name="description" content="أداة متقدمة لتوليد مدونات احترافية كاملة بالذكاء الاصطناعي مع تحسين السيو والتصميم الحديث والمحتوى عالي الجودة">

    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/main.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/icons/favicon.ico">
    <link rel="icon" type="image/svg+xml" href="assets/icons/favicon.svg">
    <link rel="apple-touch-icon" href="assets/icons/favicon.svg">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="مولد المدونات">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="browserconfig.xml">

    <!-- Schema.org markup -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "مولد المدونات الذكي",
        "description": "أداة متقدمة لتوليد مدونات احترافية كاملة بالذكاء الاصطناعي",
        "url": "https://blog-generator.ai",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>جاري تحضير الأداة...</h3>
            <p>يرجى الانتظار قليلاً</p>
        </div>
    </div>

    <!-- Header -->
    <header class="main-header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-blog brand-icon"></i>
                    <span class="brand-text">مولد المدونات الذكي</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="#home">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#features">المميزات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#generator">المولد</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#examples">أمثلة</a>
                        </li>
                    </ul>
                    <div class="navbar-nav">
                        <button class="btn btn-outline-primary btn-sm" onclick="settingsManager.showSettings()">
                            <i class="fas fa-cog"></i>
                            <span class="d-none d-md-inline">الإعدادات</span>
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">
                            <span class="highlight">مولد المدونات الذكي</span>
                            <br>أنشئ مدونة احترافية في دقائق
                        </h1>
                        <p class="hero-description">
                            أداة متطورة تعمل بالذكاء الاصطناعي لإنشاء مدونات كاملة ومتكاملة مع محتوى عالي الجودة،
                            تصميم احترافي، وتحسين كامل لمحركات البحث
                        </p>
                        <div class="hero-features">
                            <div class="feature-item">
                                <i class="fas fa-robot"></i>
                                <span>ذكاء اصطناعي متقدم</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-search"></i>
                                <span>محسن للسيو</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt"></i>
                                <span>تصميم متجاوب</span>
                            </div>
                        </div>
                        <div class="hero-actions">
                            <button class="btn btn-primary btn-lg" onclick="scrollToGenerator()">
                                <i class="fas fa-rocket"></i>
                                ابدأ الآن مجاناً
                            </button>
                            <button class="btn btn-outline-light btn-lg" onclick="showDemo()">
                                <i class="fas fa-play"></i>
                                شاهد العرض التوضيحي
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual">
                        <div class="hero-mockup">
                            <div class="mockup-screen">
                                <div class="mockup-content">
                                    <div class="mockup-header"></div>
                                    <div class="mockup-body">
                                        <div class="mockup-line"></div>
                                        <div class="mockup-line short"></div>
                                        <div class="mockup-line"></div>
                                        <div class="mockup-line medium"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">مميزات استثنائية لمدونة احترافية</h2>
                    <p class="section-subtitle">كل ما تحتاجه لإنشاء مدونة متكاملة ومحسنة</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h4>ذكاء اصطناعي متطور</h4>
                        <p>يستخدم أحدث تقنيات الذكاء الاصطناعي لتوليد محتوى عالي الجودة ومتخصص</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4>تصميم قابل للتخصيص</h4>
                        <p>اختر من مجموعة واسعة من التصاميم والألوان والخطوط العربية الجميلة</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search-plus"></i>
                        </div>
                        <h4>تحسين السيو الكامل</h4>
                        <p>تحسين شامل لمحركات البحث مع Schema markup وتحسين الأداء</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <h4>ربط داخلي ذكي</h4>
                        <p>نظام ربط داخلي احترافي يربط المقالات والصفحات بطريقة ذكية</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <h4>تصدير متعدد الصيغ</h4>
                        <p>صدّر مدونتك بصيغ متعددة: HTML، PDF، Excel، وملف مضغوط كامل</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4>تصميم متجاوب</h4>
                        <p>تصميم يتكيف مع جميع الأجهزة والشاشات بشكل مثالي</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Generator Section -->
    <section id="generator" class="generator-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">مولد المدونات</h2>
                    <p class="section-subtitle">أدخل التفاصيل وسنقوم بإنشاء مدونتك الاحترافية</p>
                </div>
            </div>

            <!-- Generator Form -->
            <div class="generator-form">
                <form id="blogGeneratorForm">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h3 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            المعلومات الأساسية
                        </h3>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="blogName" class="form-label">اسم المدونة *</label>
                                <input type="text" class="form-control" id="blogName" required
                                       placeholder="مثال: مدونة التقنية العربية">
                            </div>
                            <div class="col-md-6">
                                <label for="blogLanguage" class="form-label">لغة المدونة *</label>
                                <select class="form-select" id="blogLanguage" required>
                                    <option value="ar" selected>العربية</option>
                                    <option value="en">الإنجليزية</option>
                                    <option value="fr">الفرنسية</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Examples Section -->
    <section id="examples" class="examples-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">أمثلة على المدونات المولدة</h2>
                    <p class="section-subtitle">شاهد نماذج من المدونات الاحترافية التي تم إنشاؤها</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="example-card">
                        <div class="example-image">
                            <div class="example-mockup">
                                <div class="mockup-browser">
                                    <div class="browser-header">
                                        <div class="browser-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                    </div>
                                    <div class="browser-content">
                                        <div class="content-header"></div>
                                        <div class="content-body">
                                            <div class="content-line"></div>
                                            <div class="content-line short"></div>
                                            <div class="content-line"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="example-content">
                            <h4>مدونة التقنية</h4>
                            <p>مدونة متخصصة في التكنولوجيا والذكاء الاصطناعي</p>
                            <div class="example-stats">
                                <span><i class="fas fa-file-alt"></i> 15 مقال</span>
                                <span><i class="fas fa-folder"></i> 5 أقسام</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="example-card">
                        <div class="example-image">
                            <div class="example-mockup">
                                <div class="mockup-browser">
                                    <div class="browser-header">
                                        <div class="browser-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                    </div>
                                    <div class="browser-content">
                                        <div class="content-header"></div>
                                        <div class="content-body">
                                            <div class="content-line"></div>
                                            <div class="content-line short"></div>
                                            <div class="content-line"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="example-content">
                            <h4>مدونة الصحة</h4>
                            <p>محتوى طبي وصحي موثوق ومفيد</p>
                            <div class="example-stats">
                                <span><i class="fas fa-file-alt"></i> 12 مقال</span>
                                <span><i class="fas fa-folder"></i> 4 أقسام</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="example-card">
                        <div class="example-image">
                            <div class="example-mockup">
                                <div class="mockup-browser">
                                    <div class="browser-header">
                                        <div class="browser-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                    </div>
                                    <div class="browser-content">
                                        <div class="content-header"></div>
                                        <div class="content-body">
                                            <div class="content-line"></div>
                                            <div class="content-line short"></div>
                                            <div class="content-line"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="example-content">
                            <h4>مدونة التعليم</h4>
                            <p>موارد تعليمية وطرق تدريس حديثة</p>
                            <div class="example-stats">
                                <span><i class="fas fa-file-alt"></i> 18 مقال</span>
                                <span><i class="fas fa-folder"></i> 6 أقسام</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row g-4 text-center">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-blog"></i>
                        </div>
                        <div class="stat-number" data-count="1250">0</div>
                        <div class="stat-label">مدونة تم إنشاؤها</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number" data-count="15000">0</div>
                        <div class="stat-label">مقال تم توليده</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number" data-count="850">0</div>
                        <div class="stat-label">مستخدم راضٍ</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number" data-count="5">0</div>
                        <div class="stat-label">دقائق متوسط الإنشاء</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">الأسئلة الشائعة</h2>
                    <p class="section-subtitle">إجابات على أكثر الأسئلة شيوعاً</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    كم من الوقت يستغرق إنشاء مدونة كاملة؟
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يستغرق إنشاء مدونة كاملة مع جميع المقالات والصفحات من 3 إلى 10 دقائق حسب حجم المحتوى المطلوب وعدد المقالات.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    هل المحتوى المولد محسن لمحركات البحث؟
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    نعم، جميع المحتوى المولد محسن بالكامل لمحركات البحث مع Schema markup، meta tags، وهيكل HTML صحيح.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                    هل يمكنني تخصيص التصميم والألوان؟
                                </button>
                            </h2>
                            <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    بالطبع! يمكنك اختيار الألوان، نمط التصميم، الخطوط، ومستوى التعقيد حسب احتياجاتك.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                    ما هي صيغ التصدير المتاحة؟
                                </button>
                            </h2>
                            <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    يمكنك تصدير مدونتك بصيغ HTML، PDF، Excel، نص عادي، أو كملف مضغوط يحتوي على جميع الملفات.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <div class="footer-brand">
                        <h4>مولد المدونات الذكي</h4>
                        <p>أداة متطورة لإنشاء مدونات احترافية بالذكاء الاصطناعي. نساعدك في إنشاء محتوى عالي الجودة في دقائق معدودة.</p>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-links">
                        <h5>روابط سريعة</h5>
                        <ul>
                            <li><a href="#home">الرئيسية</a></li>
                            <li><a href="#features">المميزات</a></li>
                            <li><a href="#generator">المولد</a></li>
                            <li><a href="#examples">أمثلة</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-links">
                        <h5>المساعدة</h5>
                        <ul>
                            <li><a href="#">دليل الاستخدام</a></li>
                            <li><a href="#">الأسئلة الشائعة</a></li>
                            <li><a href="#">الدعم الفني</a></li>
                            <li><a href="#">اتصل بنا</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-links">
                        <h5>قانوني</h5>
                        <ul>
                            <li><a href="#">شروط الاستخدام</a></li>
                            <li><a href="#">سياسة الخصوصية</a></li>
                            <li><a href="#">إخلاء المسؤولية</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <div class="footer-social">
                        <h5>تابعنا</h5>
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-facebook"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                            <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="footer-copyright">
                        &copy; 2024 مولد المدونات الذكي. جميع الحقوق محفوظة.
                        <br>
                        <small>تم التطوير بواسطة الذكاء الاصطناعي المتقدم</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- External Libraries for Export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- Custom Scripts -->
    <script src="assets/js/gemini-api.js"></script>
    <script src="assets/js/blog-generator.js"></script>
    <script src="assets/js/export-manager.js"></script>
    <script src="assets/js/search-engine.js"></script>
    <script src="assets/js/icon-manager.js"></script>
    <script src="assets/js/settings-manager.js"></script>
    <script src="assets/js/notification-manager.js"></script>
    <script src="assets/js/analytics-manager.js"></script>
    <script src="assets/js/app.js"></script>

    <!-- Service Worker Registration -->
    <script>
        // تسجيل Service Worker للعمل دون اتصال (فقط عند الاستضافة على خادم)
        if ('serviceWorker' in navigator && window.location.protocol !== 'file:') {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('Service Worker registered successfully:', registration.scope);

                        // التحقق من وجود تحديثات
                        registration.addEventListener('updatefound', () => {
                            const newWorker = registration.installing;
                            newWorker.addEventListener('statechange', () => {
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // إشعار المستخدم بوجود تحديث
                                    if (window.notificationManager) {
                                        window.notificationManager.info(
                                            'يتوفر تحديث جديد للتطبيق. أعد تحميل الصفحة للحصول على أحدث إصدار.',
                                            'تحديث متاح',
                                            {
                                                persistent: true,
                                                actions: [
                                                    {
                                                        text: 'تحديث الآن',
                                                        onClick: () => window.location.reload()
                                                    }
                                                ]
                                            }
                                        );
                                    }
                                }
                            });
                        });
                    })
                    .catch(error => {
                        console.log('Service Worker registration failed:', error);
                    });
            });
        } else if (window.location.protocol === 'file:') {
            console.log('Service Worker not supported in file:// protocol. Use a local server for full PWA features.');
        }

        // إضافة أحداث PWA
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            // منع عرض الإشعار التلقائي
            e.preventDefault();
            deferredPrompt = e;

            // إظهار زر التثبيت المخصص
            if (window.notificationManager) {
                window.notificationManager.info(
                    'يمكنك تثبيت التطبيق على جهازك للوصول السريع!',
                    'تثبيت التطبيق',
                    {
                        duration: 10000,
                        actions: [
                            {
                                text: 'تثبيت',
                                onClick: () => {
                                    if (deferredPrompt) {
                                        deferredPrompt.prompt();
                                        deferredPrompt.userChoice.then((choiceResult) => {
                                            if (choiceResult.outcome === 'accepted') {
                                                console.log('User accepted the install prompt');
                                            }
                                            deferredPrompt = null;
                                        });
                                    }
                                }
                            }
                        ]
                    }
                );
            }
        });

        window.addEventListener('appinstalled', (evt) => {
            console.log('App was installed');
            if (window.notificationManager) {
                window.notificationManager.success('تم تثبيت التطبيق بنجاح!');
            }
        });
    </script>
</body>
</html>
