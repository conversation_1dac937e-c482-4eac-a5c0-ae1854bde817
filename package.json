{"name": "smart-blog-generator", "version": "1.0.0", "description": "أداة متطورة تعمل بالذكاء الاصطناعي لإنشاء مدونات احترافية كاملة ومتكاملة", "main": "index.html", "scripts": {"start": "npx http-server . -p 8080 -o", "dev": "webpack serve --mode development", "dev-simple": "npx live-server --port=8080 --open=index.html", "build": "webpack --mode production", "build-simple": "npm run minify-css && npm run minify-js", "test": "npx http-server . -p 8080 -o test-blog.html", "test-build": "npm run build && npx http-server dist -p 8080 -o", "minify-css": "npx clean-css-cli assets/css/main.css -o assets/css/main.min.css", "minify-js": "npx terser assets/js/*.js -o assets/js/bundle.min.js", "optimize-images": "npx imagemin assets/icons/* --out-dir=assets/icons/optimized", "lint": "npx eslint assets/js/*.js", "lint-fix": "npx eslint assets/js/*.js --fix", "format": "npx prettier --write assets/js/*.js assets/css/*.css *.html *.md", "format-check": "npx prettier --check assets/js/*.js assets/css/*.css *.html *.md", "validate-html": "npx html-validate *.html", "validate-all": "npm run lint && npm run format-check && npm run validate-html", "lighthouse": "npx lighthouse http://localhost:8080 --output=html --output-path=./lighthouse-report.html", "lighthouse-ci": "npx lhci autorun", "analyze": "webpack-bundle-analyzer dist/js/*.js", "serve-dist": "npx http-server dist -p 8080 -o", "preview": "npm run build && npm run serve-dist", "deploy": "npm run validate-all && npm run build && echo 'Ready for deployment'", "deploy-gh": "npm run build && npx gh-pages -d dist", "clean": "rm -rf node_modules package-lock.json dist", "clean-dist": "rm -rf dist", "reinstall": "npm run clean && npm install", "postinstall": "echo 'Installation complete! Run npm run dev to start development.'", "precommit": "npm run validate-all", "security-audit": "npm audit && npm audit fix", "update-deps": "npx npm-check-updates -u && npm install"}, "keywords": ["blog", "generator", "ai", "artificial-intelligence", "gemini", "arabic", "rtl", "seo", "content-generation", "export", "html", "pdf", "excel"], "author": {"name": "Smart Blog Generator Team", "email": "<EMAIL>", "url": "https://blog-generator.ai"}, "license": "MIT", "homepage": "https://blog-generator.ai", "repository": {"type": "git", "url": "https://github.com/blog-generator/smart-blog-generator.git"}, "bugs": {"url": "https://github.com/blog-generator/smart-blog-generator/issues"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-react-jsx-self": "^7.23.3", "@babel/plugin-transform-react-jsx-source": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/runtime": "^7.23.6", "autoprefixer": "^10.4.16", "babel-loader": "^9.1.3", "babel-plugin-transform-remove-console": "^6.9.4", "clean-css-cli": "^5.6.2", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.34.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "cssnano": "^6.0.2", "eslint": "^8.57.0", "html-loader": "^4.2.0", "html-validate": "^8.7.4", "html-webpack-plugin": "^5.6.0", "http-server": "^14.1.1", "imagemin": "^8.0.1", "imagemin-cli": "^7.0.0", "lighthouse": "^11.4.0", "live-server": "^1.2.2", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.32", "postcss-loader": "^7.3.3", "postcss-rtlcss": "^4.0.6", "prettier": "^3.1.1", "style-loader": "^3.3.3", "terser": "^5.26.0", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "workbox-webpack-plugin": "^7.0.0"}, "dependencies": {}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "config": {"port": 8080, "host": "localhost"}, "files": ["index.html", "test-blog.html", "assets/", "templates/", "README.md", "DEVELOPER.md"], "directories": {"lib": "assets/js", "doc": "docs", "test": "tests"}, "funding": {"type": "github", "url": "https://github.com/sponsors/blog-generator"}, "contributors": [{"name": "AI Development Team", "email": "<EMAIL>", "url": "https://blog-generator.ai/team"}], "private": false, "publishConfig": {"access": "public"}}