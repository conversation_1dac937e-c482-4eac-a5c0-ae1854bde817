/**
 * Notification Manager - مدير الإشعارات والتنبيهات
 * يدير عرض الإشعارات والرسائل للمستخدم
 */

class NotificationManager {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5 ثوانٍ
        this.position = 'top-end'; // top-start, top-end, bottom-start, bottom-end
        
        this.types = {
            success: {
                icon: 'fas fa-check-circle',
                class: 'alert-success',
                color: '#10b981'
            },
            error: {
                icon: 'fas fa-exclamation-circle',
                class: 'alert-danger',
                color: '#ef4444'
            },
            warning: {
                icon: 'fas fa-exclamation-triangle',
                class: 'alert-warning',
                color: '#f59e0b'
            },
            info: {
                icon: 'fas fa-info-circle',
                class: 'alert-info',
                color: '#3b82f6'
            },
            loading: {
                icon: 'fas fa-spinner fa-spin',
                class: 'alert-primary',
                color: '#6366f1'
            }
        };
        
        this.init();
    }
    
    /**
     * تهيئة مدير الإشعارات
     */
    init() {
        this.createContainer();
        this.setupStyles();
    }
    
    /**
     * إنشاء حاوي الإشعارات
     */
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'notificationContainer';
        this.container.className = `notification-container position-fixed ${this.position}`;
        this.container.style.zIndex = '9999';
        this.container.style.padding = '1rem';
        this.container.style.pointerEvents = 'none';
        
        document.body.appendChild(this.container);
    }
    
    /**
     * إعداد التنسيقات
     */
    setupStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .notification-container {
                max-width: 400px;
                width: 100%;
            }
            
            .notification-container.top-start {
                top: 0;
                left: 0;
            }
            
            .notification-container.top-end {
                top: 0;
                right: 0;
            }
            
            .notification-container.bottom-start {
                bottom: 0;
                left: 0;
            }
            
            .notification-container.bottom-end {
                bottom: 0;
                right: 0;
            }
            
            .notification-item {
                margin-bottom: 0.75rem;
                border-radius: 0.75rem;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                backdrop-filter: blur(10px);
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s ease-in-out;
                border: none;
                overflow: hidden;
                position: relative;
            }
            
            .notification-item.show {
                transform: translateX(0);
                opacity: 1;
            }
            
            .notification-item.hide {
                transform: translateX(100%);
                opacity: 0;
                margin-bottom: 0;
                padding: 0;
                height: 0;
            }
            
            .notification-content {
                display: flex;
                align-items: flex-start;
                gap: 0.75rem;
                padding: 1rem;
            }
            
            .notification-icon {
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.25rem;
            }
            
            .notification-body {
                flex: 1;
                min-width: 0;
            }
            
            .notification-title {
                font-weight: 600;
                margin-bottom: 0.25rem;
                font-size: 0.9rem;
            }
            
            .notification-message {
                font-size: 0.85rem;
                opacity: 0.9;
                line-height: 1.4;
                margin: 0;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 1.25rem;
                cursor: pointer;
                opacity: 0.6;
                transition: opacity 0.2s;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
            }
            
            .notification-close:hover {
                opacity: 1;
            }
            
            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(255, 255, 255, 0.3);
                transition: width linear;
            }
            
            .notification-actions {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.75rem;
            }
            
            .notification-action {
                padding: 0.375rem 0.75rem;
                border: 1px solid rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1);
                color: inherit;
                border-radius: 0.375rem;
                font-size: 0.8rem;
                cursor: pointer;
                transition: all 0.2s;
                text-decoration: none;
            }
            
            .notification-action:hover {
                background: rgba(255, 255, 255, 0.2);
                color: inherit;
            }
            
            @media (max-width: 768px) {
                .notification-container {
                    left: 1rem !important;
                    right: 1rem !important;
                    max-width: none;
                }
                
                .notification-item {
                    transform: translateY(-100%);
                }
                
                .notification-item.show {
                    transform: translateY(0);
                }
                
                .notification-item.hide {
                    transform: translateY(-100%);
                }
            }
        `;
        
        document.head.appendChild(style);
    }
    
    /**
     * عرض إشعار
     */
    show(options) {
        const notification = this.createNotification(options);
        this.addNotification(notification);
        return notification.id;
    }
    
    /**
     * إنشاء إشعار
     */
    createNotification(options) {
        const id = 'notification_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const type = options.type || 'info';
        const typeConfig = this.types[type];
        
        const notification = {
            id,
            type,
            title: options.title || '',
            message: options.message || '',
            duration: options.duration !== undefined ? options.duration : this.defaultDuration,
            actions: options.actions || [],
            persistent: options.persistent || false,
            onClick: options.onClick,
            onClose: options.onClose,
            element: null,
            timer: null,
            progressTimer: null
        };
        
        // إنشاء عنصر HTML
        const element = document.createElement('div');
        element.className = `alert notification-item ${typeConfig.class}`;
        element.setAttribute('data-notification-id', id);
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="${typeConfig.icon}"></i>
                </div>
                <div class="notification-body">
                    ${notification.title ? `<div class="notification-title">${notification.title}</div>` : ''}
                    <div class="notification-message">${notification.message}</div>
                    ${notification.actions.length > 0 ? this.createActionsHTML(notification.actions) : ''}
                </div>
                ${!notification.persistent ? '<button class="notification-close" onclick="notificationManager.hide(\'' + id + '\')"><i class="fas fa-times"></i></button>' : ''}
            </div>
            ${notification.duration > 0 && !notification.persistent ? '<div class="notification-progress"></div>' : ''}
        `;
        
        // إضافة أحداث
        if (notification.onClick) {
            element.style.cursor = 'pointer';
            element.addEventListener('click', (e) => {
                if (!e.target.closest('.notification-close') && !e.target.closest('.notification-action')) {
                    notification.onClick(notification);
                }
            });
        }
        
        notification.element = element;
        return notification;
    }
    
    /**
     * إنشاء HTML للأزرار
     */
    createActionsHTML(actions) {
        return `
            <div class="notification-actions">
                ${actions.map(action => `
                    <button class="notification-action" onclick="${action.onClick ? action.onClick.toString() : ''}">
                        ${action.icon ? `<i class="${action.icon} me-1"></i>` : ''}
                        ${action.text}
                    </button>
                `).join('')}
            </div>
        `;
    }
    
    /**
     * إضافة إشعار إلى الحاوي
     */
    addNotification(notification) {
        // إزالة الإشعارات الزائدة
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.removeNotification(oldest.id);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
        
        // تأثير الظهور
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);
        
        // إعداد المؤقت للإخفاء التلقائي
        if (notification.duration > 0 && !notification.persistent) {
            this.startAutoHide(notification);
        }
    }
    
    /**
     * بدء الإخفاء التلقائي
     */
    startAutoHide(notification) {
        const progressBar = notification.element.querySelector('.notification-progress');
        
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${notification.duration}ms linear`;
            
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }
        
        notification.timer = setTimeout(() => {
            this.hide(notification.id);
        }, notification.duration);
    }
    
    /**
     * إيقاف الإخفاء التلقائي
     */
    pauseAutoHide(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && notification.timer) {
            clearTimeout(notification.timer);
            notification.timer = null;
            
            const progressBar = notification.element.querySelector('.notification-progress');
            if (progressBar) {
                progressBar.style.transition = 'none';
            }
        }
    }
    
    /**
     * استئناف الإخفاء التلقائي
     */
    resumeAutoHide(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.timer && !notification.persistent) {
            this.startAutoHide(notification);
        }
    }
    
    /**
     * إخفاء إشعار
     */
    hide(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;
        
        // إيقاف المؤقت
        if (notification.timer) {
            clearTimeout(notification.timer);
        }
        
        // تأثير الإخفاء
        notification.element.classList.add('hide');
        
        setTimeout(() => {
            this.removeNotification(notificationId);
        }, 300);
    }
    
    /**
     * إزالة إشعار
     */
    removeNotification(notificationId) {
        const index = this.notifications.findIndex(n => n.id === notificationId);
        if (index === -1) return;
        
        const notification = this.notifications[index];
        
        // استدعاء callback الإغلاق
        if (notification.onClose) {
            notification.onClose(notification);
        }
        
        // إزالة العنصر
        if (notification.element && notification.element.parentNode) {
            notification.element.parentNode.removeChild(notification.element);
        }
        
        // إزالة من المصفوفة
        this.notifications.splice(index, 1);
    }
    
    /**
     * مسح جميع الإشعارات
     */
    clear() {
        this.notifications.forEach(notification => {
            this.hide(notification.id);
        });
    }
    
    /**
     * تحديث موضع الحاوي
     */
    setPosition(position) {
        this.position = position;
        this.container.className = `notification-container position-fixed ${position}`;
    }
    
    /**
     * إشعارات مختصرة
     */
    success(message, title = 'نجح!', options = {}) {
        return this.show({
            type: 'success',
            title,
            message,
            ...options
        });
    }
    
    error(message, title = 'خطأ!', options = {}) {
        return this.show({
            type: 'error',
            title,
            message,
            duration: 8000, // مدة أطول للأخطاء
            ...options
        });
    }
    
    warning(message, title = 'تحذير!', options = {}) {
        return this.show({
            type: 'warning',
            title,
            message,
            duration: 6000,
            ...options
        });
    }
    
    info(message, title = 'معلومة', options = {}) {
        return this.show({
            type: 'info',
            title,
            message,
            ...options
        });
    }
    
    loading(message, title = 'جاري التحميل...', options = {}) {
        return this.show({
            type: 'loading',
            title,
            message,
            persistent: true,
            ...options
        });
    }
    
    /**
     * إشعار تأكيد
     */
    confirm(message, title = 'تأكيد', options = {}) {
        return new Promise((resolve) => {
            this.show({
                type: 'warning',
                title,
                message,
                persistent: true,
                actions: [
                    {
                        text: 'نعم',
                        icon: 'fas fa-check',
                        onClick: () => {
                            this.hide(notificationId);
                            resolve(true);
                        }
                    },
                    {
                        text: 'لا',
                        icon: 'fas fa-times',
                        onClick: () => {
                            this.hide(notificationId);
                            resolve(false);
                        }
                    }
                ],
                ...options
            });
        });
    }
    
    /**
     * إشعار التقدم
     */
    progress(message, title = 'جاري المعالجة...', initialProgress = 0) {
        const notificationId = this.show({
            type: 'loading',
            title,
            message: `${message} (${initialProgress}%)`,
            persistent: true
        });
        
        return {
            id: notificationId,
            update: (progress, newMessage) => {
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification) {
                    const messageElement = notification.element.querySelector('.notification-message');
                    if (messageElement) {
                        messageElement.textContent = `${newMessage || message} (${progress}%)`;
                    }
                }
            },
            complete: (finalMessage = 'تم بنجاح!') => {
                this.hide(notificationId);
                this.success(finalMessage);
            },
            error: (errorMessage = 'حدث خطأ!') => {
                this.hide(notificationId);
                this.error(errorMessage);
            }
        };
    }
}

// إنشاء مثيل عام من مدير الإشعارات
window.notificationManager = new NotificationManager();
