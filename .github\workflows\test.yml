name: Test and Quality Assurance

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint-and-format:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run ESLint
      run: npm run lint
      
    - name: Check Prettier formatting
      run: npm run format-check
      
    - name: Validate HTML
      run: npm run validate-html

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level moderate
      
    - name: Check for vulnerabilities
      run: |
        if npm audit --audit-level high --json | jq '.vulnerabilities | length' | grep -q '^0$'; then
          echo "No high-severity vulnerabilities found"
        else
          echo "High-severity vulnerabilities detected"
          exit 1
        fi

  build-test:
    name: Build Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Check build output
      run: |
        if [ -d "dist" ]; then
          echo "Build successful - dist directory created"
          ls -la dist/
        else
          echo "Build failed - no dist directory"
          exit 1
        fi
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: dist/
        retention-days: 7

  lighthouse-audit:
    name: Lighthouse Performance Audit
    runs-on: ubuntu-latest
    needs: build-test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
        path: dist/
        
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.12.x
      
    - name: Run Lighthouse CI
      run: |
        npm run serve-dist &
        sleep 10
        lhci autorun --upload.target=temporary-public-storage
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  cross-browser-test:
    name: Cross-Browser Testing
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        browser: [chrome, firefox]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Start development server
      run: |
        npm run start &
        sleep 10
      
    - name: Run browser tests
      run: |
        # Install browser testing tools
        npm install -g puppeteer
        
        # Create simple test script
        cat > browser-test.js << 'EOF'
        const puppeteer = require('puppeteer');
        
        (async () => {
          const browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
          });
          
          const page = await browser.newPage();
          
          try {
            // Test main page
            await page.goto('http://localhost:8080', { waitUntil: 'networkidle0' });
            
            const title = await page.title();
            console.log('Page title:', title);
            
            // Check if main elements exist
            const generator = await page.$('#generator');
            if (!generator) throw new Error('Generator section not found');
            
            const navbar = await page.$('.navbar');
            if (!navbar) throw new Error('Navigation bar not found');
            
            console.log('✅ Main page test passed');
            
            // Test blog test page
            await page.goto('http://localhost:8080/test-blog.html', { waitUntil: 'networkidle0' });
            
            const testTitle = await page.title();
            console.log('Test page title:', testTitle);
            
            console.log('✅ Test page loaded successfully');
            
            // Test examples page
            await page.goto('http://localhost:8080/examples/basic-usage.html', { waitUntil: 'networkidle0' });
            
            const exampleTitle = await page.title();
            console.log('Examples page title:', exampleTitle);
            
            console.log('✅ Examples page loaded successfully');
            
          } catch (error) {
            console.error('❌ Test failed:', error);
            process.exit(1);
          } finally {
            await browser.close();
          }
        })();
        EOF
        
        node browser-test.js

  accessibility-test:
    name: Accessibility Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install axe-core CLI
      run: npm install -g @axe-core/cli
      
    - name: Start development server
      run: |
        npm run start &
        sleep 10
      
    - name: Run accessibility tests
      run: |
        axe http://localhost:8080 --exit
        axe http://localhost:8080/test-blog.html --exit
        axe http://localhost:8080/examples/basic-usage.html --exit

  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Start development server
      run: |
        npm run start &
        sleep 10
      
    - name: Run performance tests
      run: |
        # Install performance testing tools
        npm install -g clinic
        
        # Create performance test
        cat > perf-test.js << 'EOF'
        const https = require('http');
        
        function testPageLoad(url) {
          return new Promise((resolve, reject) => {
            const start = Date.now();
            
            const req = https.get(url, (res) => {
              let data = '';
              
              res.on('data', (chunk) => {
                data += chunk;
              });
              
              res.on('end', () => {
                const loadTime = Date.now() - start;
                console.log(`${url} loaded in ${loadTime}ms`);
                
                if (loadTime > 3000) {
                  reject(new Error(`Page load too slow: ${loadTime}ms`));
                } else {
                  resolve(loadTime);
                }
              });
            });
            
            req.on('error', reject);
            req.setTimeout(5000, () => {
              reject(new Error('Request timeout'));
            });
          });
        }
        
        (async () => {
          try {
            await testPageLoad('http://localhost:8080');
            await testPageLoad('http://localhost:8080/test-blog.html');
            await testPageLoad('http://localhost:8080/examples/basic-usage.html');
            
            console.log('✅ All performance tests passed');
          } catch (error) {
            console.error('❌ Performance test failed:', error);
            process.exit(1);
          }
        })();
        EOF
        
        node perf-test.js

  notify-results:
    name: Notify Test Results
    runs-on: ubuntu-latest
    needs: [lint-and-format, security-audit, build-test, lighthouse-audit, cross-browser-test, accessibility-test, performance-test]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.lint-and-format.result == 'success' && needs.security-audit.result == 'success' && needs.build-test.result == 'success' }}
      run: echo "✅ All tests passed successfully!"
      
    - name: Notify failure
      if: ${{ needs.lint-and-format.result == 'failure' || needs.security-audit.result == 'failure' || needs.build-test.result == 'failure' }}
      run: |
        echo "❌ Some tests failed:"
        echo "Lint and Format: ${{ needs.lint-and-format.result }}"
        echo "Security Audit: ${{ needs.security-audit.result }}"
        echo "Build Test: ${{ needs.build-test.result }}"
        echo "Lighthouse: ${{ needs.lighthouse-audit.result }}"
        echo "Cross-Browser: ${{ needs.cross-browser-test.result }}"
        echo "Accessibility: ${{ needs.accessibility-test.result }}"
        echo "Performance: ${{ needs.performance-test.result }}"
        exit 1
