/**
 * Blog Generator - مولد المدونات الاحترافي
 * يستخدم الذكاء الاصطناعي لإنشاء مدونات كاملة ومتكاملة
 */

class BlogGenerator {
    constructor() {
        this.currentBlog = null;
        this.generationProgress = 0;
        this.isGenerating = false;
        this.progressCallback = null;
        
        // إعدادات افتراضية
        this.defaultSettings = {
            language: 'ar',
            complexity: 'متوسط',
            articlesPerSection: 3,
            wordsPerParagraph: 150,
            headingsPerArticle: 4,
            includeTables: true,
            includeLists: true,
            tone: 'احترافي',
            includeImages: true,
            includeSearch: true,
            includePrivacyPages: true,
            seoOptimized: true
        };
        
        // قوالب البرومبت
        this.prompts = {
            blogStructure: this.getBlogStructurePrompt(),
            articleContent: this.getArticleContentPrompt(),
            seoContent: this.getSEOContentPrompt(),
            pageContent: this.getPageContentPrompt()
        };
    }
    
    /**
     * بدء عملية توليد المدونة
     */
    async generateBlog(blogData, progressCallback) {
        this.isGenerating = true;
        this.generationProgress = 0;
        this.progressCallback = progressCallback;
        
        try {
            this.updateProgress(5, 'بدء عملية التوليد...');
            
            // 1. إنشاء هيكل المدونة
            this.updateProgress(10, 'إنشاء هيكل المدونة...');
            const blogStructure = await this.generateBlogStructure(blogData);
            
            // 2. توليد المحتوى الأساسي
            this.updateProgress(20, 'توليد المحتوى الأساسي...');
            const basicContent = await this.generateBasicContent(blogData, blogStructure);
            
            // 3. إنشاء الأقسام والمقالات
            this.updateProgress(30, 'إنشاء الأقسام والمقالات...');
            const sections = await this.generateSections(blogData, blogStructure);
            
            // 4. توليد المقالات
            this.updateProgress(50, 'توليد المقالات...');
            const articles = await this.generateArticles(blogData, sections);
            
            // 5. إنشاء الصفحات الإضافية
            this.updateProgress(70, 'إنشاء الصفحات الإضافية...');
            const additionalPages = await this.generateAdditionalPages(blogData);
            
            // 6. تحسين السيو
            this.updateProgress(80, 'تحسين السيو...');
            const seoData = await this.generateSEOData(blogData, articles);
            
            // 7. إنشاء الروابط الداخلية
            this.updateProgress(90, 'إنشاء الروابط الداخلية...');
            const internalLinks = this.generateInternalLinks(articles, sections);
            
            // 8. تجميع المدونة النهائية
            this.updateProgress(95, 'تجميع المدونة النهائية...');
            this.currentBlog = {
                metadata: blogData,
                structure: blogStructure,
                content: basicContent,
                sections: sections,
                articles: articles,
                additionalPages: additionalPages,
                seo: seoData,
                internalLinks: internalLinks,
                generatedAt: new Date().toISOString()
            };
            
            this.updateProgress(100, 'تم إنشاء المدونة بنجاح!');
            return this.currentBlog;
            
        } catch (error) {
            console.error('خطأ في توليد المدونة:', error);
            throw new Error(`فشل في توليد المدونة: ${error.message}`);
        } finally {
            this.isGenerating = false;
        }
    }
    
    /**
     * تحديث شريط التقدم
     */
    updateProgress(percentage, message) {
        this.generationProgress = percentage;
        if (this.progressCallback) {
            this.progressCallback(percentage, message);
        }
    }
    
    /**
     * توليد هيكل المدونة
     */
    async generateBlogStructure(blogData) {
        const prompt = this.prompts.blogStructure
            .replace('{blogName}', blogData.blogName)
            .replace('{language}', blogData.language)
            .replace('{sections}', blogData.sections || 'تقنية، صحة، تعليم، أعمال، ترفيه')
            .replace('{complexity}', blogData.complexity || 'متوسط');
        
        const response = await window.geminiAPI.queueRequest(prompt, 'high');
        return this.parseStructureResponse(response);
    }
    
    /**
     * توليد المحتوى الأساسي للمدونة
     */
    async generateBasicContent(blogData, structure) {
        const heroPrompt = `
        أنشئ محتوى Hero Section احترافي لمدونة "${blogData.blogName}" باللغة ${blogData.language}.
        
        المطلوب:
        - عنوان جذاب وقوي
        - وصف مختصر ومقنع
        - دعوة للعمل واضحة
        - نبذة عن المدونة
        
        يجب أن يكون المحتوى:
        - جذاب ومقنع
        - محسن للسيو
        - يعكس هوية المدونة
        - مناسب للجمهور المستهدف
        
        أعطني النتيجة في تنسيق JSON:
        {
            "heroTitle": "العنوان الرئيسي",
            "heroSubtitle": "العنوان الفرعي",
            "heroDescription": "الوصف",
            "callToAction": "دعوة للعمل",
            "aboutSection": "نبذة عن المدونة"
        }
        `;
        
        const response = await window.geminiAPI.queueRequest(heroPrompt, 'high');
        return this.parseJSONResponse(response);
    }
    
    /**
     * توليد الأقسام
     */
    async generateSections(blogData, structure) {
        const sectionsData = [];
        const sectionNames = (blogData.sections || 'تقنية، صحة، تعليم، أعمال، ترفيه').split('،');
        
        for (let i = 0; i < Math.min(sectionNames.length, 5); i++) {
            const sectionName = sectionNames[i].trim();
            
            const sectionPrompt = `
            أنشئ محتوى قسم "${sectionName}" لمدونة "${blogData.blogName}" باللغة ${blogData.language}.
            
            المطلوب:
            - عنوان القسم
            - وصف القسم
            - Hero خاص بالقسم
            - كلمات مفتاحية للقسم
            - وصف ميتا للقسم
            
            أعطني النتيجة في تنسيق JSON:
            {
                "name": "اسم القسم",
                "title": "عنوان القسم",
                "description": "وصف القسم",
                "heroContent": "محتوى Hero القسم",
                "keywords": ["كلمة1", "كلمة2", "كلمة3"],
                "metaDescription": "وصف ميتا"
            }
            `;
            
            const response = await window.geminiAPI.queueRequest(sectionPrompt, 'normal');
            const sectionData = this.parseJSONResponse(response);
            sectionData.id = this.generateSlug(sectionName);
            sectionsData.push(sectionData);
        }
        
        return sectionsData;
    }
    
    /**
     * توليد المقالات
     */
    async generateArticles(blogData, sections) {
        const allArticles = [];
        const keywords = this.parseKeywords(blogData.keywords || '');
        
        for (const section of sections) {
            const sectionArticles = [];
            const articlesCount = parseInt(blogData.articlesPerSection) || 3;
            
            // توزيع الكلمات المفتاحية على الأقسام
            const sectionKeywords = this.distributeKeywords(keywords, section.name, articlesCount);
            
            for (let i = 0; i < articlesCount; i++) {
                const keyword = sectionKeywords[i] || `${section.name} - مقال ${i + 1}`;
                
                const articlePrompt = this.prompts.articleContent
                    .replace('{keyword}', keyword)
                    .replace('{sectionName}', section.name)
                    .replace('{blogName}', blogData.blogName)
                    .replace('{language}', blogData.language)
                    .replace('{wordsPerParagraph}', blogData.wordsPerParagraph || 150)
                    .replace('{headingsCount}', blogData.headingsPerArticle || 4)
                    .replace('{tone}', blogData.tone || 'احترافي')
                    .replace('{includeTables}', blogData.includeTables ? 'نعم' : 'لا')
                    .replace('{includeLists}', blogData.includeLists ? 'نعم' : 'لا');
                
                const response = await window.geminiAPI.queueRequest(articlePrompt, 'normal');
                const article = this.parseArticleResponse(response, keyword, section.id);
                sectionArticles.push(article);
                
                // تحديث التقدم
                const currentProgress = 50 + ((allArticles.length + sectionArticles.length) / (sections.length * articlesCount)) * 20;
                this.updateProgress(currentProgress, `توليد مقال: ${article.title}`);
            }
            
            allArticles.push(...sectionArticles);
        }
        
        return allArticles;
    }
    
    /**
     * توليد الصفحات الإضافية
     */
    async generateAdditionalPages(blogData) {
        const pages = [];
        
        if (blogData.includePrivacyPages !== false) {
            const pagesPrompt = `
            أنشئ محتوى الصفحات الإضافية التالية لمدونة "${blogData.blogName}" باللغة ${blogData.language}:
            
            1. صفحة "من نحن"
            2. صفحة "سياسة الخصوصية"
            3. صفحة "شروط الاستخدام"
            4. صفحة "اتصل بنا"
            
            أعطني النتيجة في تنسيق JSON مع محتوى كامل لكل صفحة.
            `;
            
            const response = await window.geminiAPI.queueRequest(pagesPrompt, 'low');
            const additionalPages = this.parseJSONResponse(response);
            pages.push(...Object.values(additionalPages));
        }
        
        return pages;
    }
    
    /**
     * توليد بيانات السيو
     */
    async generateSEOData(blogData, articles) {
        const seoPrompt = this.prompts.seoContent
            .replace('{blogName}', blogData.blogName)
            .replace('{language}', blogData.language)
            .replace('{articlesCount}', articles.length);
        
        const response = await window.geminiAPI.queueRequest(seoPrompt, 'normal');
        return this.parseJSONResponse(response);
    }
    
    /**
     * إنشاء الروابط الداخلية
     */
    generateInternalLinks(articles, sections) {
        const links = [];
        
        // ربط المقالات ببعضها البعض
        articles.forEach((article, index) => {
            const relatedArticles = articles
                .filter((a, i) => i !== index && a.sectionId === article.sectionId)
                .slice(0, 3);
            
            article.relatedArticles = relatedArticles.map(a => ({
                title: a.title,
                url: a.url,
                excerpt: a.excerpt
            }));
        });
        
        return links;
    }
    
    /**
     * تحليل الكلمات المفتاحية
     */
    parseKeywords(keywordsString) {
        if (!keywordsString) return [];
        return keywordsString.split(/[،,\n]/).map(k => k.trim()).filter(k => k.length > 0);
    }
    
    /**
     * توزيع الكلمات المفتاحية على الأقسام
     */
    distributeKeywords(keywords, sectionName, count) {
        const sectionKeywords = keywords.filter(k => 
            k.toLowerCase().includes(sectionName.toLowerCase()) ||
            this.isKeywordRelatedToSection(k, sectionName)
        );
        
        // إذا لم نجد كلمات مفتاحية مناسبة، استخدم كلمات عامة
        while (sectionKeywords.length < count) {
            sectionKeywords.push(`${sectionName} - موضوع ${sectionKeywords.length + 1}`);
        }
        
        return sectionKeywords.slice(0, count);
    }
    
    /**
     * فحص ارتباط الكلمة المفتاحية بالقسم
     */
    isKeywordRelatedToSection(keyword, sectionName) {
        const relationMap = {
            'تقنية': ['تكنولوجيا', 'برمجة', 'حاسوب', 'ذكاء', 'تطبيق'],
            'صحة': ['طب', 'علاج', 'دواء', 'مرض', 'صحي'],
            'تعليم': ['دراسة', 'تعلم', 'مدرسة', 'جامعة', 'تدريب'],
            'أعمال': ['شركة', 'تجارة', 'استثمار', 'مال', 'اقتصاد'],
            'ترفيه': ['لعبة', 'فيلم', 'موسيقى', 'رياضة', 'سفر']
        };
        
        const relatedWords = relationMap[sectionName] || [];
        return relatedWords.some(word => keyword.toLowerCase().includes(word));
    }
    
    /**
     * إنشاء slug من النص
     */
    generateSlug(text) {
        return text
            .toLowerCase()
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-z0-9]/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    
    /**
     * تحليل استجابة الهيكل
     */
    parseStructureResponse(response) {
        try {
            return JSON.parse(response);
        } catch (error) {
            return { structure: response };
        }
    }
    
    /**
     * تحليل استجابة JSON
     */
    parseJSONResponse(response) {
        try {
            return JSON.parse(response);
        } catch (error) {
            console.warn('فشل في تحليل JSON، استخدام النص الخام:', error);
            return { content: response };
        }
    }
    
    /**
     * تحليل استجابة المقال
     */
    parseArticleResponse(response, keyword, sectionId) {
        const article = this.parseJSONResponse(response);
        
        return {
            id: this.generateSlug(keyword),
            title: article.title || keyword,
            content: article.content || response,
            excerpt: article.excerpt || '',
            keywords: article.keywords || [keyword],
            metaDescription: article.metaDescription || '',
            sectionId: sectionId,
            url: `/articles/${this.generateSlug(keyword)}.html`,
            publishDate: new Date().toISOString(),
            author: 'مولد المدونات الذكي',
            readingTime: this.calculateReadingTime(article.content || response)
        };
    }
    
    /**
     * حساب وقت القراءة
     */
    calculateReadingTime(content) {
        const wordsPerMinute = 200;
        const wordCount = content.split(/\s+/).length;
        const minutes = Math.ceil(wordCount / wordsPerMinute);
        return `${minutes} دقيقة`;
    }
    
    /**
     * الحصول على برومبت هيكل المدونة
     */
    getBlogStructurePrompt() {
        return `
        أنشئ هيكل مدونة احترافي ومتكامل لمدونة بعنوان "{blogName}" باللغة {language}.
        
        الأقسام المطلوبة: {sections}
        مستوى التعقيد: {complexity}
        
        أعطني هيكل شامل يتضمن:
        - تخطيط الصفحة الرئيسية
        - هيكل الأقسام
        - نوع المحتوى لكل قسم
        - استراتيجية الربط الداخلي
        
        النتيجة في تنسيق JSON.
        `;
    }
    
    /**
     * الحصول على برومبت محتوى المقال
     */
    getArticleContentPrompt() {
        return `
        اكتب مقال احترافي ومتكامل حول "{keyword}" لقسم "{sectionName}" في مدونة "{blogName}" باللغة {language}.
        
        المواصفات:
        - عدد الكلمات في كل فقرة: {wordsPerParagraph}
        - عدد العناوين الفرعية: {headingsCount}
        - نبرة المقال: {tone}
        - تضمين جداول: {includeTables}
        - تضمين قوائم: {includeLists}
        
        المطلوب:
        - عنوان جذاب ومحسن للسيو
        - مقدمة قوية
        - محتوى منظم بعناوين فرعية
        - خاتمة مفيدة
        - وصف ميتا
        - كلمات مفتاحية
        
        أعطني النتيجة في تنسيق JSON مع HTML منسق للمحتوى.
        `;
    }
    
    /**
     * الحصول على برومبت السيو
     */
    getSEOContentPrompt() {
        return `
        أنشئ بيانات السيو الكاملة لمدونة "{blogName}" باللغة {language} التي تحتوي على {articlesCount} مقال.
        
        المطلوب:
        - Schema markup للمدونة
        - بيانات Open Graph
        - Twitter Cards
        - Sitemap structure
        - Robots.txt content
        
        النتيجة في تنسيق JSON.
        `;
    }
    
    /**
     * الحصول على برومبت محتوى الصفحات
     */
    getPageContentPrompt() {
        return `
        أنشئ محتوى الصفحات الأساسية لمدونة "{blogName}" باللغة {language}.
        
        الصفحات المطلوبة:
        - من نحن
        - سياسة الخصوصية
        - شروط الاستخدام
        - اتصل بنا
        
        النتيجة في تنسيق JSON مع HTML منسق.
        `;
    }
}

// إنشاء مثيل عام من مولد المدونات
window.blogGenerator = new BlogGenerator();
