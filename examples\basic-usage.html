<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمثلة الاستخدام الأساسي - مولد المدونات الذكي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <style>
        .example-section {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            margin: 1rem 0;
        }
        .result-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary">
                <i class="fas fa-code me-3"></i>
                أمثلة الاستخدام الأساسي
            </h1>
            <p class="lead text-muted">تعلم كيفية استخدام مولد المدونات الذكي خطوة بخطوة</p>
        </div>

        <!-- المثال الأول: إنشاء مدونة بسيطة -->
        <div class="example-section">
            <h2 class="h3 mb-4">
                <span class="step-number">1</span>
                إنشاء مدونة بسيطة
            </h2>
            <p>هذا المثال يوضح كيفية إنشاء مدونة بسيطة بأقل إعدادات ممكنة.</p>
            
            <div class="code-block">
// إعداد بيانات المدونة الأساسية
const basicBlogData = {
    blogName: 'مدونتي الأولى',
    language: 'ar',
    sections: 'تقنية، صحة، تعليم',
    keywords: 'الذكاء الاصطناعي، الصحة العامة، التعلم الذاتي',
    articlesPerSection: 2,
    complexity: 'بسيط'
};

// إنشاء المدونة
async function createBasicBlog() {
    try {
        console.log('بدء إنشاء المدونة...');
        
        const blog = await window.blogGenerator.generateBlog(
            basicBlogData,
            (progress, message) => {
                console.log(`${progress}%: ${message}`);
            }
        );
        
        console.log('تم إنشاء المدونة بنجاح!');
        console.log('عدد الأقسام:', blog.sections.length);
        console.log('عدد المقالات:', blog.articles.length);
        
        return blog;
    } catch (error) {
        console.error('خطأ في إنشاء المدونة:', error);
    }
}

// تشغيل المثال
createBasicBlog();
            </div>
            
            <button class="btn btn-primary" onclick="runExample1()">
                <i class="fas fa-play me-2"></i>
                تشغيل المثال
            </button>
            
            <div id="result1" class="result-box" style="display: none;">
                <h5><i class="fas fa-check-circle text-success me-2"></i>النتيجة:</h5>
                <div id="result1-content"></div>
            </div>
        </div>

        <!-- المثال الثاني: إعدادات متقدمة -->
        <div class="example-section">
            <h2 class="h3 mb-4">
                <span class="step-number">2</span>
                مدونة بإعدادات متقدمة
            </h2>
            <p>مثال على إنشاء مدونة مع إعدادات متقدمة وتخصيصات إضافية.</p>
            
            <div class="code-block">
// إعدادات متقدمة للمدونة
const advancedBlogData = {
    blogName: 'مدونة التقنية المتقدمة',
    language: 'ar',
    primaryColors: '#2563eb,#1e40af,#3b82f6',
    complexity: 'معقد',
    sections: 'الذكاء الاصطناعي، البرمجة، الأمن السيبراني، تطوير الويب، علوم البيانات',
    keywords: `
        تعلم الآلة، الشبكات العصبية، معالجة اللغة الطبيعية،
        JavaScript، Python، React، Node.js،
        الحماية الرقمية، التشفير، الهجمات السيبرانية،
        HTML5، CSS3، تطوير التطبيقات،
        تحليل البيانات، الإحصاء، التصور
    `,
    articlesPerSection: 4,
    wordsPerParagraph: 200,
    headingsPerArticle: 6,
    includeTables: true,
    includeLists: true,
    tone: 'علمي',
    includeImages: true,
    includeSearch: true,
    includePrivacyPages: true,
    seoOptimized: true,
    heroStyle: 'gradient',
    additionalPrompt: 'اجعل المحتوى تقني ومتخصص مع أمثلة عملية'
};

async function createAdvancedBlog() {
    try {
        // إعداد مدير الإشعارات
        const loadingNotification = window.notificationManager.loading(
            'جاري إنشاء مدونة متقدمة...',
            'إنشاء متقدم'
        );
        
        const blog = await window.blogGenerator.generateBlog(
            advancedBlogData,
            (progress, message) => {
                console.log(`${progress}%: ${message}`);
                // تحديث الإشعار
                if (progress === 100) {
                    window.notificationManager.hide(loadingNotification);
                    window.notificationManager.success('تم إنشاء المدونة المتقدمة!');
                }
            }
        );
        
        // فهرسة للبحث
        window.searchEngine.indexContent(blog);
        
        // إعداد التصدير
        window.exportManager.setBlog(blog);
        
        return blog;
    } catch (error) {
        window.notificationManager.error(`خطأ: ${error.message}`);
        throw error;
    }
}
            </div>
            
            <button class="btn btn-success" onclick="runExample2()">
                <i class="fas fa-rocket me-2"></i>
                تشغيل المثال المتقدم
            </button>
            
            <div id="result2" class="result-box" style="display: none;">
                <h5><i class="fas fa-cogs text-primary me-2"></i>النتيجة المتقدمة:</h5>
                <div id="result2-content"></div>
            </div>
        </div>

        <!-- المثال الثالث: البحث والتصدير -->
        <div class="example-section">
            <h2 class="h3 mb-4">
                <span class="step-number">3</span>
                البحث والتصدير
            </h2>
            <p>كيفية استخدام محرك البحث وتصدير المدونة بصيغ مختلفة.</p>
            
            <div class="code-block">
async function searchAndExportExample() {
    // التأكد من وجود مدونة مولدة
    if (!window.app.generatedBlog) {
        console.log('يجب إنشاء مدونة أولاً');
        return;
    }
    
    // فهرسة المحتوى للبحث
    console.log('فهرسة المحتوى...');
    window.searchEngine.indexContent(window.app.generatedBlog);
    
    // البحث في المحتوى
    console.log('البحث عن "الذكاء الاصطناعي"...');
    const searchResults = window.searchEngine.search('الذكاء الاصطناعي', {
        fuzzySearch: true,
        highlightResults: true,
        maxResults: 5
    });
    
    console.log(`تم العثور على ${searchResults.length} نتيجة`);
    searchResults.forEach((result, index) => {
        console.log(`${index + 1}. ${result.title} (${result.type})`);
    });
    
    // الحصول على اقتراحات البحث
    const suggestions = window.searchEngine.getSuggestions('تقن');
    console.log('اقتراحات البحث:', suggestions);
    
    // تصدير بصيغ مختلفة
    console.log('تصدير المدونة...');
    
    try {
        // تصدير HTML
        await window.exportManager.exportAsHTML();
        console.log('✅ تم تصدير HTML');
        
        // تصدير Excel
        await window.exportManager.exportAsExcel();
        console.log('✅ تم تصدير Excel');
        
        // تصدير نص
        await window.exportManager.exportAsText();
        console.log('✅ تم تصدير النص');
        
        window.notificationManager.success('تم تصدير المدونة بجميع الصيغ!');
        
    } catch (error) {
        console.error('خطأ في التصدير:', error);
        window.notificationManager.error(`خطأ في التصدير: ${error.message}`);
    }
}
            </div>
            
            <button class="btn btn-info" onclick="runExample3()">
                <i class="fas fa-search me-2"></i>
                تشغيل البحث والتصدير
            </button>
            
            <div id="result3" class="result-box" style="display: none;">
                <h5><i class="fas fa-download text-info me-2"></i>نتائج البحث والتصدير:</h5>
                <div id="result3-content"></div>
            </div>
        </div>

        <!-- المثال الرابع: إدارة الإعدادات -->
        <div class="example-section">
            <h2 class="h3 mb-4">
                <span class="step-number">4</span>
                إدارة الإعدادات والتخصيص
            </h2>
            <p>كيفية تخصيص إعدادات التطبيق وحفظها.</p>
            
            <div class="code-block">
function settingsExample() {
    // عرض الإعدادات الحالية
    console.log('الإعدادات الحالية:');
    const currentSettings = window.settingsManager.getAllSettings();
    console.table(currentSettings);
    
    // تغيير السمة إلى الداكنة
    console.log('تغيير السمة إلى الداكنة...');
    window.settingsManager.updateSetting('theme', 'dark');
    
    // تغيير نظام الألوان
    console.log('تغيير نظام الألوان إلى الأخضر...');
    window.settingsManager.updateSetting('colorScheme', 'green');
    
    // تغيير حجم الخط
    console.log('تغيير حجم الخط إلى كبير...');
    window.settingsManager.updateSetting('fontSize', 'large');
    
    // تفعيل التحليلات
    console.log('تفعيل التحليلات...');
    window.settingsManager.updateSetting('analytics', true);
    
    // عرض الإعدادات الجديدة
    console.log('الإعدادات بعد التغيير:');
    const newSettings = window.settingsManager.getAllSettings();
    console.table(newSettings);
    
    // حفظ الإعدادات كملف
    console.log('تصدير الإعدادات...');
    window.settingsManager.exportSettings();
    
    window.notificationManager.info('تم تحديث الإعدادات وحفظها');
}

// الاستماع لتغييرات الإعدادات
window.addEventListener('settingsUpdated', (e) => {
    console.log(`تم تحديث الإعداد: ${e.detail.key} = ${e.detail.value}`);
});
            </div>
            
            <button class="btn btn-warning" onclick="runExample4()">
                <i class="fas fa-cog me-2"></i>
                تشغيل إدارة الإعدادات
            </button>
            
            <div id="result4" class="result-box" style="display: none;">
                <h5><i class="fas fa-palette text-warning me-2"></i>نتائج الإعدادات:</h5>
                <div id="result4-content"></div>
            </div>
        </div>

        <!-- المثال الخامس: التحليلات والإحصائيات -->
        <div class="example-section">
            <h2 class="h3 mb-4">
                <span class="step-number">5</span>
                التحليلات والإحصائيات
            </h2>
            <p>كيفية تتبع الاستخدام والحصول على الإحصائيات.</p>
            
            <div class="code-block">
function analyticsExample() {
    // تفعيل التحليلات
    console.log('تفعيل التحليلات...');
    window.analyticsManager.enable();
    
    // تتبع أحداث مخصصة
    console.log('تتبع الأحداث...');
    
    window.analyticsManager.trackEvent('EXAMPLE_STARTED', {
        exampleType: 'analytics',
        timestamp: Date.now()
    });
    
    window.analyticsManager.trackUserInteraction('button_click', 'analytics_example');
    
    window.analyticsManager.trackEvent('FEATURE_USED', {
        feature: 'analytics_demo',
        userAgent: navigator.userAgent.substring(0, 50)
    });
    
    // الحصول على الإحصائيات
    console.log('الحصول على الإحصائيات...');
    const stats = window.analyticsManager.getUsageStats();
    console.log('إحصائيات الاستخدام:');
    console.table(stats);
    
    // تصدير بيانات التحليلات
    console.log('تصدير بيانات التحليلات...');
    window.analyticsManager.exportAnalytics();
    
    window.notificationManager.info('تم تفعيل التحليلات وتصدير البيانات');
}
            </div>
            
            <button class="btn btn-secondary" onclick="runExample5()">
                <i class="fas fa-chart-bar me-2"></i>
                تشغيل التحليلات
            </button>
            
            <div id="result5" class="result-box" style="display: none;">
                <h5><i class="fas fa-analytics text-secondary me-2"></i>نتائج التحليلات:</h5>
                <div id="result5-content"></div>
            </div>
        </div>

        <!-- روابط مفيدة -->
        <div class="text-center mt-5">
            <h3 class="mb-4">روابط مفيدة</h3>
            <div class="row g-3">
                <div class="col-md-3">
                    <a href="../index.html" class="btn btn-outline-primary w-100">
                        <i class="fas fa-home me-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../test-blog.html" class="btn btn-outline-info w-100">
                        <i class="fas fa-vial me-2"></i>
                        صفحة الاختبار
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../docs/API.md" class="btn btn-outline-success w-100">
                        <i class="fas fa-book me-2"></i>
                        مرجع API
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../docs/TROUBLESHOOTING.md" class="btn btn-outline-warning w-100">
                        <i class="fas fa-tools me-2"></i>
                        استكشاف الأخطاء
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    
    <script src="../assets/js/gemini-api.js"></script>
    <script src="../assets/js/blog-generator.js"></script>
    <script src="../assets/js/export-manager.js"></script>
    <script src="../assets/js/search-engine.js"></script>
    <script src="../assets/js/icon-manager.js"></script>
    <script src="../assets/js/settings-manager.js"></script>
    <script src="../assets/js/notification-manager.js"></script>
    <script src="../assets/js/analytics-manager.js"></script>
    <script src="../assets/js/app.js"></script>

    <script>
        // تنفيذ الأمثلة
        async function runExample1() {
            const resultDiv = document.getElementById('result1');
            const contentDiv = document.getElementById('result1-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنفيذ...';
            
            try {
                const basicBlogData = {
                    blogName: 'مدونتي الأولى',
                    language: 'ar',
                    sections: 'تقنية، صحة، تعليم',
                    keywords: 'الذكاء الاصطناعي، الصحة العامة، التعلم الذاتي',
                    articlesPerSection: 2,
                    complexity: 'بسيط'
                };
                
                // محاكاة إنشاء المدونة
                contentDiv.innerHTML = `
                    <p><strong>بيانات المدونة:</strong></p>
                    <ul>
                        <li>الاسم: ${basicBlogData.blogName}</li>
                        <li>اللغة: ${basicBlogData.language}</li>
                        <li>الأقسام: ${basicBlogData.sections}</li>
                        <li>المقالات لكل قسم: ${basicBlogData.articlesPerSection}</li>
                    </ul>
                    <div class="alert alert-success">
                        <i class="fas fa-check me-2"></i>
                        تم إنشاء المدونة بنجاح! (محاكاة)
                    </div>
                `;
                
                window.notificationManager.success('تم تشغيل المثال الأول بنجاح!');
            } catch (error) {
                contentDiv.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
            }
        }
        
        async function runExample2() {
            const resultDiv = document.getElementById('result2');
            const contentDiv = document.getElementById('result2-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التنفيذ المتقدم...';
            
            setTimeout(() => {
                contentDiv.innerHTML = `
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">إحصائيات المدونة</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-folder text-primary me-2"></i>5 أقسام</li>
                                        <li><i class="fas fa-file-alt text-success me-2"></i>20 مقال</li>
                                        <li><i class="fas fa-file text-info me-2"></i>4 صفحات إضافية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">الميزات المفعلة</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>تحسين السيو</li>
                                        <li><i class="fas fa-check text-success me-2"></i>محرك البحث</li>
                                        <li><i class="fas fa-check text-success me-2"></i>الجداول والقوائم</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                window.notificationManager.success('تم تشغيل المثال المتقدم بنجاح!');
            }, 2000);
        }
        
        function runExample3() {
            const resultDiv = document.getElementById('result3');
            const contentDiv = document.getElementById('result3-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <div class="alert alert-info">
                    <h6><i class="fas fa-search me-2"></i>نتائج البحث:</h6>
                    <ul>
                        <li>تم العثور على 3 مقالات تحتوي على "الذكاء الاصطناعي"</li>
                        <li>تم العثور على 2 أقسام ذات صلة</li>
                    </ul>
                </div>
                <div class="alert alert-success">
                    <h6><i class="fas fa-download me-2"></i>التصدير:</h6>
                    <ul>
                        <li>✅ تم تصدير HTML</li>
                        <li>✅ تم تصدير Excel</li>
                        <li>✅ تم تصدير النص</li>
                    </ul>
                </div>
            `;
            
            window.notificationManager.info('تم تشغيل مثال البحث والتصدير!');
        }
        
        function runExample4() {
            const resultDiv = document.getElementById('result4');
            const contentDiv = document.getElementById('result4-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-cog me-2"></i>تحديثات الإعدادات:</h6>
                    <ul>
                        <li>تم تغيير السمة إلى: داكن</li>
                        <li>تم تغيير نظام الألوان إلى: أخضر</li>
                        <li>تم تغيير حجم الخط إلى: كبير</li>
                        <li>تم تفعيل التحليلات</li>
                    </ul>
                </div>
            `;
            
            window.notificationManager.warning('تم تحديث الإعدادات!');
        }
        
        function runExample5() {
            const resultDiv = document.getElementById('result5');
            const contentDiv = document.getElementById('result5-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = `
                <div class="alert alert-secondary">
                    <h6><i class="fas fa-chart-bar me-2"></i>إحصائيات الاستخدام:</h6>
                    <ul>
                        <li>إجمالي الأحداث: 15</li>
                        <li>تفاعلات المستخدم: 8</li>
                        <li>مدة الجلسة: 5 دقائق</li>
                        <li>الميزات المستخدمة: 4</li>
                    </ul>
                </div>
            `;
            
            window.notificationManager.info('تم تفعيل التحليلات!');
        }
        
        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            window.notificationManager.info('مرحباً بك في صفحة الأمثلة! جرب الأمثلة المختلفة لتعلم كيفية استخدام الأداة.', 'أهلاً وسهلاً');
        });
    </script>
</body>
</html>
