{"env": {"browser": true, "es2021": true, "node": false}, "extends": ["eslint:recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "globals": {"bootstrap": "readonly", "XLSX": "readonly", "JSZip": "readonly", "jspdf": "readonly", "geminiAPI": "writable", "blogGenerator": "writable", "exportManager": "writable", "searchEngine": "writable", "iconManager": "writable", "settingsManager": "writable", "notificationManager": "writable", "analyticsManager": "writable", "app": "writable"}, "rules": {"indent": ["error", 4], "linebreak-style": ["error", "unix"], "quotes": ["error", "single"], "semi": ["error", "always"], "no-unused-vars": ["warn"], "no-console": ["warn"], "no-debugger": ["error"], "no-alert": ["warn"], "no-eval": ["error"], "no-implied-eval": ["error"], "no-new-func": ["error"], "no-script-url": ["error"], "no-void": ["error"], "no-with": ["error"], "curly": ["error", "all"], "eqeqeq": ["error", "always"], "no-eq-null": ["error"], "no-floating-decimal": ["error"], "no-multi-spaces": ["error"], "no-multi-str": ["error"], "no-new": ["error"], "no-new-wrappers": ["error"], "no-octal-escape": ["error"], "no-param-reassign": ["error"], "no-proto": ["error"], "no-return-assign": ["error"], "no-self-compare": ["error"], "no-sequences": ["error"], "no-throw-literal": ["error"], "no-unmodified-loop-condition": ["error"], "no-unused-expressions": ["error"], "no-useless-call": ["error"], "no-useless-concat": ["error"], "no-useless-escape": ["error"], "radix": ["error"], "wrap-iife": ["error"], "yoda": ["error"], "no-delete-var": ["error"], "no-label-var": ["error"], "no-restricted-globals": ["error"], "no-shadow": ["error"], "no-shadow-restricted-names": ["error"], "no-undef": ["error"], "no-undef-init": ["error"], "no-undefined": ["error"], "no-use-before-define": ["error"], "array-bracket-spacing": ["error", "never"], "block-spacing": ["error", "always"], "brace-style": ["error", "1tbs"], "camelcase": ["error"], "comma-dangle": ["error", "never"], "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": ["error", "last"], "computed-property-spacing": ["error", "never"], "consistent-this": ["error", "self"], "eol-last": ["error"], "func-call-spacing": ["error", "never"], "func-name-matching": ["error"], "func-style": ["error", "declaration", {"allowArrowFunctions": true}], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "keyword-spacing": ["error", {"before": true, "after": true}], "line-comment-position": ["error", {"position": "above"}], "lines-around-comment": ["error", {"beforeBlockComment": true}], "max-depth": ["error", 4], "max-len": ["error", {"code": 120, "ignoreUrls": true}], "max-nested-callbacks": ["error", 3], "max-params": ["error", 5], "max-statements-per-line": ["error", {"max": 1}], "new-cap": ["error"], "new-parens": ["error"], "newline-after-var": ["error", "always"], "no-array-constructor": ["error"], "no-bitwise": ["error"], "no-continue": ["error"], "no-inline-comments": ["error"], "no-lonely-if": ["error"], "no-mixed-operators": ["error"], "no-multiple-empty-lines": ["error", {"max": 2}], "no-negated-condition": ["error"], "no-nested-ternary": ["error"], "no-new-object": ["error"], "no-plusplus": ["error", {"allowForLoopAfterthoughts": true}], "no-tabs": ["error"], "no-trailing-spaces": ["error"], "no-underscore-dangle": ["error"], "no-unneeded-ternary": ["error"], "no-whitespace-before-property": ["error"], "object-curly-spacing": ["error", "always"], "one-var": ["error", "never"], "operator-assignment": ["error", "always"], "operator-linebreak": ["error", "before"], "padded-blocks": ["error", "never"], "quote-props": ["error", "as-needed"], "require-jsdoc": ["warn", {"require": {"FunctionDeclaration": true, "MethodDefinition": true, "ClassDeclaration": true}}], "semi-spacing": ["error", {"before": false, "after": true}], "space-before-blocks": ["error", "always"], "space-before-function-paren": ["error", "never"], "space-in-parens": ["error", "never"], "space-infix-ops": ["error"], "space-unary-ops": ["error", {"words": true, "nonwords": false}], "spaced-comment": ["error", "always"], "unicode-bom": ["error", "never"], "arrow-body-style": ["error", "as-needed"], "arrow-parens": ["error", "as-needed"], "arrow-spacing": ["error", {"before": true, "after": true}], "constructor-super": ["error"], "generator-star-spacing": ["error", {"before": false, "after": true}], "no-class-assign": ["error"], "no-confusing-arrow": ["error"], "no-const-assign": ["error"], "no-dupe-class-members": ["error"], "no-duplicate-imports": ["error"], "no-new-symbol": ["error"], "no-restricted-imports": ["error"], "no-this-before-super": ["error"], "no-useless-computed-key": ["error"], "no-useless-constructor": ["error"], "no-useless-rename": ["error"], "no-var": ["error"], "object-shorthand": ["error", "always"], "prefer-arrow-callback": ["error"], "prefer-const": ["error"], "prefer-destructuring": ["error", {"object": true, "array": false}], "prefer-numeric-literals": ["error"], "prefer-rest-params": ["error"], "prefer-spread": ["error"], "prefer-template": ["error"], "rest-spread-spacing": ["error", "never"], "sort-imports": ["error", {"ignoreCase": true}], "symbol-description": ["error"], "template-curly-spacing": ["error", "never"], "yield-star-spacing": ["error", {"before": false, "after": true}]}, "overrides": [{"files": ["test-*.js", "*.test.js"], "rules": {"no-console": "off", "no-alert": "off"}}]}